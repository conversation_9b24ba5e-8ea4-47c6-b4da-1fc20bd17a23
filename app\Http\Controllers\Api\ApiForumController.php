<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Thread;
use Illuminate\Support\Str;

class ApiForumController extends Controller
{
    public function create(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
        ]);
        $accountId = auth('account')->id();
        if (!$accountId) {
            return response()->json(['success' => false, 'message' => 'Bạn cần đăng nhập!'], 401);
        }
        $thread = Thread::create([
            'title' => $request->title,
            'slug' => Str::slug($request->title) . '-' . Str::random(5),
            'content' => $request->content,
            'account_id' => $accountId,
            'views' => 0,
        ]);
        return response()->json([
            'success' => true,
            'message' => 'Đăng chủ đề thành công!',
            'redirect' => route('forum.show', $thread->slug),
        ]);
    }

    public function reply(Request $request)
    {
        $request->validate([
            'thread_id' => 'required|exists:threads,id',
            'content' => 'required|string',
            'parent_id' => 'nullable|exists:replies,id',
        ]);
        $accountId = auth('account')->id();
        if (!$accountId) {
            return response()->json(['success' => false, 'message' => 'Bạn cần đăng nhập!'], 401);
        }
        $reply = \App\Models\Reply::create([
            'thread_id' => $request->thread_id,
            'account_id' => $accountId,
            'content' => $request->content,
            'parent_id' => $request->parent_id,
        ]);
        return response()->json(['success' => true, 'message' => 'Gửi phản hồi thành công!']);
    }
} 
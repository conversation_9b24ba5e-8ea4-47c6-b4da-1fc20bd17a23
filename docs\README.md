# Tài liệu dự án

Th<PERSON> mục này chứa các tài liệu liên quan đến dự án.

## Cấu trúc thư mục

- `installation.md` - Hướng dẫn cài đặt dự án
- `usage.md` - Hướng dẫn sử dụng dự án
- `api.md` - Tài liệu API

## Giới thiệu

Đây là dự án Laravel sử dụng Tailwind CSS. Các tài liệu trong thư mục này sẽ giúp bạn hiểu rõ hơn về cách cài đặt, cấu hình và sử dụng dự án.

## Các bước cấu hình & chạy dự án

1. **Clone source code về máy**
2. **Cài đặt Composer** (nếu chưa có): https://getcomposer.org/
3. **Cài đặt các package PHP:**
   ```
   composer install
   ```
4. **Copy file cấu hình môi trường:**
   ```
   cp .env.example .env
   ```
5. **Tạo application key:**
   ```
   php artisan key:generate
   ```
6. **Cấu hình database trong file `.env`**
7. **Chạy migrate để tạo bảng:**
   ```
   php artisan migrate

   Nếu có rồi
   php artisan migrate:fresh
   ```
8. **Chạy seed dữ liệu mẫu (nếu có):**
   ```
   php artisan db:seed
   ```
9. **Cài đặt Node.js và npm** (nếu chưa có): https://nodejs.org/
10. **Cài đặt các package JS:**
    ```
    npm install
    ```
11. **Build assets:**
    ```
    npm run build
    ```
php artisan livewire:publish --assets
## Password
<EMAIL>
DcRGlUxitcNt5uqK
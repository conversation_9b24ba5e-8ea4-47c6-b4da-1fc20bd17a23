<?php

namespace App\Filament\Widgets;

use App\Models\Post;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Carbon;

class PostsChart extends ChartWidget
{
    protected static ?string $heading = 'Posts Published';
    
    protected static ?string $pollingInterval = null;
    
    protected static ?string $maxHeight = '300px';
    
    protected int | string | array $columnSpan = 'full';

    protected function getData(): array
    {
        $data = $this->getPostsPerMonth();
        
        return [
            'datasets' => [
                [
                    'label' => 'Posts Published',
                    'data' => $data['counts'],
                    'backgroundColor' => '#36A2EB',
                    'borderColor' => '#36A2EB',
                ],
            ],
            'labels' => $data['months'],
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }
    
    private function getPostsPerMonth(): array
    {
        $now = Carbon::now();
        $months = collect();
        $counts = collect();
        
        // Get data for the last 12 months
        for ($i = 11; $i >= 0; $i--) {
            $month = Carbon::now()->subMonths($i)->format('M Y');
            $count = Post::whereYear('created_at', Carbon::now()->subMonths($i)->year)
                ->whereMonth('created_at', Carbon::now()->subMonths($i)->month)
                ->count();
                
            $months->push($month);
            $counts->push($count);
        }
        
        return [
            'counts' => $counts->toArray(),
            'months' => $months->toArray(),
        ];
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Gift extends Model
{
    protected $fillable = [
        'name',
        'description',
        'image',
        'value',
        'quantity',
        'total_quantity',
        'is_active',
        'available_from',
        'available_until',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'available_from' => 'date',
        'available_until' => 'date',
    ];

    /**
     * Get the redemptions for the gift.
     */
    public function redemptions()
    {
        return $this->hasMany(GiftRedemption::class);
    }
}

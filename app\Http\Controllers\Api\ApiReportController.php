<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Models\Report;

class ApiReportController extends Controller
{
    public function store(Request $request)
    {
        if (!Auth::guard('account')->check()) {
            return response()->json(['message' => 'Chưa đăng nhập'], 401);
        }

        $validator = Validator::make($request->all(), [
            'image' => 'required|image|max:4096',
            'description' => 'required|string',
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
            'gmap_link' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['message' => 'Dữ liệu không hợp lệ', 'errors' => $validator->errors()], 422);
        }

        // <PERSON><PERSON> lý upload ảnh
        $imagePath = $request->file('image')->store('reports', 'public');

        $report = Report::create([
            'created_by' => Auth::guard('account')->id(),
            'image' => $imagePath,
            'description' => $request->description,
            'latitude' => $request->latitude,
            'longitude' => $request->longitude,
            'gmap_link' => $request->gmap_link,
            'status' => 'pending',
        ]);

        return response()->json(['message' => 'Báo cáo đã được gửi thành công!', 'redirect' => url('/map')], 201);
    }
} 
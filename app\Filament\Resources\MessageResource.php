<?php

namespace App\Filament\Resources;

use App\Filament\Resources\MessageResource\Pages;
use App\Models\Message;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class MessageResource extends Resource
{
    protected static ?string $model = Message::class;

    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-left-right';
    
    protected static ?string $navigationGroup = 'Content Management';
    
    protected static ?string $navigationLabel = 'Comments';
    
    protected static ?int $navigationSort = 5;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('account_id')
                    ->relationship('account', 'username') // Thay 'username' bằng 'full_name' hoặc 'email'
                    ->searchable()
                    ->preload()
                    ->required(),
                    
                Textarea::make('content')
                    ->label('Content')
                    ->required()
                    ->columnSpanFull(),
                    
                Toggle::make('is_approved')
                    ->label('Approved')
                    ->default(false),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('account.username') // Thay 'account.username' bằng 'account.full_name'
                    ->label('Account')
                    ->searchable()
                    ->sortable(),
                    
                TextColumn::make('content')
                    ->label('Content')
                    ->limit(50)
                    ->searchable(),
                    
                TextColumn::make('messageable_type')
                    ->label('Content Type')
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'App\\Models\\Post' => 'Post',
                        'App\\Models\\Event' => 'Event',
                        default => class_basename($state),
                    })
                    ->sortable(),
                    
                TextColumn::make('messageable_id')
                    ->label('Content ID')
                    ->sortable(),
                    
                IconColumn::make('is_approved')
                    ->label('Approved')
                    ->boolean()
                    ->sortable(),
                    
                TextColumn::make('created_at')
                    ->label('Created At')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->filters([
                TernaryFilter::make('is_approved')
                    ->label('Approval Status')
                    ->placeholder('All Comments')
                    ->trueLabel('Approved')
                    ->falseLabel('Not Approved'),
                    
                SelectFilter::make('messageable_type')
                    ->label('Content Type')
                    ->options([
                        'App\\Models\\Post' => 'Post',
                        'App\\Models\\Event' => 'Event',
                    ]),
            ])
            ->actions([
                ActionGroup::make([
                    Action::make('approve')
                        ->label('Approve')
                        ->icon('heroicon-o-check')
                        ->color('success')
                        ->visible(fn (Message $record): bool => !$record->is_approved)
                        ->action(fn (Message $record) => $record->update(['is_approved' => true])),
                        
                    Action::make('reject')
                        ->label('Reject')
                        ->icon('heroicon-o-x-mark')
                        ->color('danger')
                        ->visible(fn (Message $record): bool => $record->is_approved)
                        ->action(fn (Message $record) => $record->update(['is_approved' => false])),
                        
                    DeleteAction::make(),
                ]),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMessages::route('/'),
            'create' => Pages\CreateMessage::route('/create'),
            'edit' => Pages\EditMessage::route('/{record}/edit'),
        ];
    }
}

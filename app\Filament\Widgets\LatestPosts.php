<?php

namespace App\Filament\Widgets;

use App\Models\Post;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ImageColumn;

class LatestPosts extends BaseWidget
{
    protected static ?string $heading = 'Latest Posts';
    
    protected int | string | array $columnSpan = 'full';
    
    protected function getTableQuery(): Builder
    {
        return Post::query()->latest()->limit(5);
    }

    protected function getTableColumns(): array
    {
        return [
            ImageColumn::make('featured_image')
                ->circular()
                ->size(40),
            TextColumn::make('title')
                ->searchable()
                ->limit(50),
            TextColumn::make('category.name')
                ->badge()
                ->color('primary'),
            TextColumn::make('published_at')
                ->dateTime()
                ->sortable(),
            TextColumn::make('created_at')
                ->dateTime()
                ->sortable()
                ->toggleable(isToggledHiddenByDefault: true),
        ];
    }
}

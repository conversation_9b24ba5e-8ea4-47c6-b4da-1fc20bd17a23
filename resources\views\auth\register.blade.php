<x-guest-layout>
    <div class="min-h-screen flex flex-col justify-center items-center bg-gradient-to-br from-[#0EA5E9]/10 via-[#10B981]/10 to-[#4ADE80]/10 py-12 px-4">
        <div class="w-full max-w-md bg-white rounded-2xl shadow-2xl p-8 md:p-10">
            <h2 class="text-3xl font-bold text-center mb-8 bg-gradient-to-r from-[#10B981] to-[#0EA5E9] bg-clip-text text-transparent">
                {{ trans_db('nav.dang_ky') ?? 'Đ<PERSON>ng ký tài khoản' }}
            </h2>

            <form method="POST" action="{{ route('register.post', ['locale' => app()->getLocale()]) }}" class="space-y-6">
                @csrf

                <!-- Email -->
                <div>
                    <x-input-label for="email" :value="trans_db('form.email') ?? 'Email'" class="text-[#10B981] font-semibold" />
                    <x-text-input id="email"
                        class="block mt-1 w-full rounded-lg border border-[#10B981]/30 focus:border-[#10B981] focus:ring-[#10B981]"
                        type="email" name="email" :value="old('email')" required autofocus
                        placeholder="{{ trans_db('form.nhap_email') ?? 'Nhập email' }}" />
                    <x-input-error :messages="$errors->get('email')" class="mt-2" />
                </div>

                <!-- Username -->
                <div>
                    <x-input-label for="username" :value="trans_db('nav.ten_dang_nhap') ?? 'Tên đăng nhập'" class="text-[#10B981] font-semibold" />
                    <x-text-input id="username"
                        class="block mt-1 w-full rounded-lg border border-[#10B981]/30 focus:border-[#10B981] focus:ring-[#10B981]"
                        type="text" name="username" :value="old('username')" required
                        placeholder="{{ trans_db('nav.nhap_ten_dang_nhap') ?? 'Nhập tên đăng nhập' }}" />
                    <x-input-error :messages="$errors->get('username')" class="mt-2" />
                </div>

                <!-- Full Name -->
                <div>
                    <x-input-label for="full_name" :value="trans_db('form.ho_va_ten') ?? 'Họ và tên'" class="text-[#10B981] font-semibold" />
                    <x-text-input id="full_name"
                        class="block mt-1 w-full rounded-lg border border-[#10B981]/30 focus:border-[#10B981] focus:ring-[#10B981]"
                        type="text" name="full_name" :value="old('full_name')" required
                        placeholder="{{ trans_db('form.nhap_ho_va_ten') ?? 'Nhập họ và tên' }}" />
                    <x-input-error :messages="$errors->get('full_name')" class="mt-2" />
                </div>

                <!-- Password -->
                <div>
                    <x-input-label for="password" :value="trans_db('form.mat_khau') ?? 'Mật khẩu'" class="text-[#10B981] font-semibold" />
                    <x-text-input id="password"
                        class="block mt-1 w-full rounded-lg border border-[#10B981]/30 focus:border-[#10B981] focus:ring-[#10B981]"
                        type="password" name="password" required autocomplete="new-password"
                        placeholder="{{ trans_db('form.nhap_mat_khau') ?? 'Nhập mật khẩu' }}" />
                    <x-input-error :messages="$errors->get('password')" class="mt-2" />
                </div>

                <!-- Confirm Password -->
                <div>
                    <x-input-label for="password_confirmation" :value="trans_db('form.confirm_password') ?? 'Xác nhận mật khẩu'" class="text-[#10B981] font-semibold" />
                    <x-text-input id="password_confirmation"
                        class="block mt-1 w-full rounded-lg border border-[#10B981]/30 focus:border-[#10B981] focus:ring-[#10B981]"
                        type="password" name="password_confirmation" required autocomplete="new-password"
                        placeholder="{{ trans_db('form.nhap_lai_mat_khau') ?? 'Nhập lại mật khẩu' }}" />
                    <x-input-error :messages="$errors->get('password_confirmation')" class="mt-2" />
                </div>

                <!-- Submit Button -->
                <div>
                    <button type="submit"
                        class="w-full py-3 rounded-lg text-white font-semibold bg-gradient-to-r from-[#10B981] to-[#0EA5E9] hover:from-[#0EA5E9] hover:to-[#10B981] transition duration-300">
                        {{ trans_db('action.tao_tai_khoan') ?? 'Tạo tài khoản' }}
                    </button>
                </div>

                <!-- Login Link -->
                <div class="text-center">
                    <p class="text-gray-600">
                        {{ trans_db('nav.da_co_tai_khoan') ?? 'Đã có tài khoản?' }}
                        <a href="{{ route('login', ['locale' => app()->getLocale()]) }}" class="text-[#10B981] hover:text-[#0EA5E9] font-semibold transition duration-300">
                            {{ trans_db('nav.dang_nhap') ?? 'Đăng nhập' }}
                        </a>
                    </p>
                </div>
            </form>
        </div>
    </div>
</x-guest-layout>

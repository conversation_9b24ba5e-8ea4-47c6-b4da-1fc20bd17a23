<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class RewardRedemption extends Model
{
    protected $fillable = [
        'account_id',
        'reward_id',
        'quantity',
        'point_used',
        'status',
    ];

    /**
     * Get the reward that owns the redemption.
     */
    public function reward()
    {
        return $this->belongsTo(Reward::class);
    }
}

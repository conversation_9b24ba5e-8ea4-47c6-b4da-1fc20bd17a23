@extends('layouts.app')

@section('title', trans_db('general.bao_cao_diem_rac_moi') ?? 'Báo cáo điểm rác mới')

@section('header', trans_db('general.bao_cao_diem_rac_moi') ?? 'Báo cáo điểm rác mới')

@section('content')
    <div
        class="w-full min-h-screen flex flex-col justify-center items-center bg-gradient-to-br from-[#0EA5E9]/10 via-[#10B981]/10 to-[#4ADE80]/10 py-12 px-4">
        <div class="w-full max-w-md bg-white rounded-2xl shadow-2xl p-8 md:p-10">
            <h2 class="text-3xl font-bold text-center mb-8 bg-gradient-to-r from-[#10B981] to-[#0EA5E9] bg-clip-text text-transparent">
                {{ trans_db('general.bao_cao_diem_rac_moi') ?? '<PERSON><PERSON>o cáo điểm rác mới' }}
            </h2>

            <form method="POST" action="{{ route('pages.report.submit', ['locale' => app()->getLocale()]) }}" enctype="multipart/form-data" class="space-y-6">
                @csrf
                <div>
                    <label for="image" class="block font-semibold mb-1 text-[#10B981]">
                        {{ trans_db('general.anh_diem_rac') ?? 'Ảnh điểm rác' }} <span class="text-red-500">*</span>
                    </label>
                    <input type="file" name="image" id="image" accept="image/*" required
                        class="block mt-1 w-full rounded-lg border border-[#10B981]/30 focus:border-[#10B981] focus:ring-[#10B981] px-3 py-2" />
                </div>
                <div>
                    <label for="description" class="block font-semibold mb-1 text-[#10B981]">
                        {{ trans_db('general.mo_ta') ?? 'Mô tả' }} <span class="text-red-500">*</span>
                    </label>
                    <textarea name="description" id="description" rows="3" required
                        placeholder="{{ trans_db('general.vi_du_dong_rac_lon_canh_truong_hoc') ?? 'Ví dụ: Đống rác lớn cạnh trường học' }}"
                        class="block mt-1 w-full rounded-lg border border-[#10B981]/30 focus:border-[#10B981] focus:ring-[#10B981] px-3 py-2"></textarea>
                </div>
                <div>
                    <label class="block font-semibold mb-1 text-[#10B981]">
                        {{ trans_db('general.vi_tri_gps') ?? 'Vị trí GPS' }} <span class="text-red-500">*</span>
                    </label>
                    <div class="flex space-x-2">
                        <input type="text" name="latitude" id="latitude"
                            placeholder="{{ trans_db('general.vi_do_latitude') ?? 'Vĩ độ (Latitude)' }}" required
                            class="w-1/2 rounded-lg border border-[#10B981]/30 focus:border-[#10B981] focus:ring-[#10B981] px-3 py-2" />
                        <input type="text" name="longitude" id="longitude"
                            placeholder="{{ trans_db('general.kinh_do_longitude') ?? 'Kinh độ (Longitude)' }}" required
                            class="w-1/2 rounded-lg border border-[#10B981]/30 focus:border-[#10B981] focus:ring-[#10B981] px-3 py-2" />
                    </div>
                    <small id="gps-status" class="text-gray-500"></small>
                    <div class="mt-2">
                        <small class="text-gray-500">
                            {{ trans_db('common.hoac_dan_link_google_maps_vi_tri_diem_rac') ?? 'Hoặc dán link Google Maps vị trí điểm rác' }}
                        </small>
                        <input type="url" name="gmap_link" id="gmap_link"
                            placeholder="https://maps.google.com/..."
                            class="block mt-1 w-full rounded-lg border border-[#10B981]/30 focus:border-[#10B981] focus:ring-[#10B981] px-3 py-2" />
                    </div>
                    <div class="mt-2">
                        <small class="text-gray-500">
                            {{ trans_db('form.gui_bao_cao') ?? 'Gửi báo cáo' }} -
                            <a href="{{ route('pages.map', ['locale' => app()->getLocale()]) }}" class="text-[#10B981] hover:underline">
                                {{ trans_db('general.kham_pha_ban_do') ?? 'Khám phá bản đồ' }}
                            </a>
                        </small>
                    </div>
                </div>
                @if (Auth::guard('account')->check())
                    <div class="pt-4">
                        <button type="submit"
                            class="w-full bg-gradient-to-r from-[#10B981] to-[#0EA5E9] text-white font-bold py-3 px-4 rounded-lg hover:from-[#059669] hover:to-[#0284C7] transition duration-300 transform hover:scale-105">
                            {{ trans_db('form.gui_bao_cao') ?? 'Gửi báo cáo' }}
                        </button>
                    </div>
                @else
                    <div class="bg-gray-50 rounded-2xl p-6 mt-6 text-center">
                        <p class="text-gray-600 mb-4">
                            {{ trans_db('auth.dang_nhap_de_gui_bao_cao') ?? 'Đăng nhập để gửi báo cáo' }}
                        </p>
                        <a href="{{ route('login', ['locale' => app()->getLocale()]) }}"
                           class="inline-block px-6 py-2 bg-gradient-to-r from-[#10B981] to-[#0EA5E9] text-white rounded-lg font-semibold hover:from-[#0EA5E9] hover:to-[#10B981] transition">
                            {{ trans_db('auth.login') ?? 'Đăng nhập' }}
                        </a>
                    </div>
                @endif
            </form>
        </div>
    </div>

    <script>
        // Auto-detect GPS location
        document.addEventListener('DOMContentLoaded', function() {
            const latInput = document.getElementById('latitude');
            const lngInput = document.getElementById('longitude');
            const gpsStatus = document.getElementById('gps-status');

            if (navigator.geolocation) {
                gpsStatus.textContent = '{{ trans_db("general.dang_lay_vi_tri") ?? "Đang lấy vị trí..." }}';

                navigator.geolocation.getCurrentPosition(
                    function(position) {
                        latInput.value = position.coords.latitude.toFixed(7);
                        lngInput.value = position.coords.longitude.toFixed(7);
                        gpsStatus.textContent = '{{ trans_db("general.da_lay_vi_tri_thanh_cong") ?? "Đã lấy vị trí thành công!" }}';
                        gpsStatus.className = 'text-green-500';
                    },
                    function(error) {
                        gpsStatus.textContent = '{{ trans_db("general.khong_the_lay_vi_tri") ?? "Không thể lấy vị trí. Vui lòng nhập thủ công." }}';
                        gpsStatus.className = 'text-red-500';
                    }
                );
            } else {
                gpsStatus.textContent = '{{ trans_db("general.trinh_duyet_khong_ho_tro_gps") ?? "Trình duyệt không hỗ trợ GPS." }}';
                gpsStatus.className = 'text-red-500';
            }

            // Handle Google Maps link parsing
            const gmapInput = document.getElementById('gmap_link');
            gmapInput.addEventListener('blur', function() {
                const url = this.value;
                if (url && url.includes('google.com/maps')) {
                    // Try to extract coordinates from Google Maps URL
                    const coordMatch = url.match(/@(-?\d+\.\d+),(-?\d+\.\d+)/);
                    if (coordMatch) {
                        latInput.value = coordMatch[1];
                        lngInput.value = coordMatch[2];
                        gpsStatus.textContent = '{{ trans_db("general.da_lay_toa_do_tu_google_maps") ?? "Đã lấy tọa độ từ Google Maps!" }}';
                        gpsStatus.className = 'text-green-500';
                    }
                }
            });
        });
    </script>
@endsection

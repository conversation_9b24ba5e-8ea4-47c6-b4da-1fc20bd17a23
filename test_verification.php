<?php

require 'vendor/autoload.php';
$app = require 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

// Set locale
app()->setLocale('vi');

// Create a mock account
$account = new \App\Models\Account();
$account->id = 1;
$account->email = '<EMAIL>';

// Create notification
$notification = new \App\Notifications\VerifyAccountEmail('vi');
$url = $notification->verificationUrl($account);

echo 'Generated URL: ' . $url . PHP_EOL;
echo 'Contains locale: ' . (str_contains($url, 'locale=vi') ? 'YES' : 'NO') . PHP_EOL;
echo 'Contains id: ' . (str_contains($url, 'id=1') ? 'YES' : 'NO') . PHP_EOL;
echo 'Contains hash: ' . (str_contains($url, 'hash=') ? 'YES' : 'NO') . PHP_EOL;

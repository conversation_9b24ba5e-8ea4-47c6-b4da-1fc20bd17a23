<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\ApiRegisterController;
use App\Http\Controllers\Api\ApiProfileController;
use App\Http\Controllers\Api\ApiAuthController;
use App\Http\Controllers\Api\ApiReportController;
use App\Http\Controllers\Api\ApiEventController;
use App\Http\Controllers\Api\ApiForumController;
use App\Http\Controllers\GiftController;

use App\Http\Controllers\Api\ApiMapController;


/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::post('register/email', [ApiRegisterController::class, 'sendVerificationCode']);
Route::post('register/verify', [ApiRegisterController::class, 'checkVerificationCode']);
Route::post('register/complete', [ApiRegisterController::class, 'completeRegistration']);

Route::middleware(['web'])->post('/login', [ApiAuthController::class, 'login']);
Route::middleware(['web', 'auth:account'])->post('/reports', [ApiReportController::class, 'store']);

Route::middleware(['web', 'auth:account'])->post('/logout', [ApiAuthController::class, 'logout'])->name('logout');
Route::middleware(['web', 'auth:account'])->post('/profile/update', [ApiProfileController::class, 'update']);
Route::middleware(['web', 'auth:account'])->post('/event/register', [ApiEventController::class, 'register']);
Route::middleware(['web', 'auth:account'])->post('/forum/create', [ApiForumController::class, 'create']);
Route::middleware(['web', 'auth:account'])->post('/forum/reply', [ApiForumController::class, 'reply']);

// Gift routes
Route::middleware(['web', 'auth:account'])->post('/gifts/redeem', [GiftController::class, 'redeem'])->middleware(['auth:account', 'verified'])->name('gifts.redeem');
Route::middleware(['web', 'auth:account'])->get('/gifts/history', [GiftController::class, 'history'])->middleware(['auth:account', 'verified'])->name('gifts.history');



// Group routes APP
Route::post('/app/login', [ApiAuthController::class, 'login']);
Route::post('/app/register', [ApiAuthController::class, 'appRegister']);
Route::post('/app/forgot-password', [ApiAuthController::class, 'appForgotPassword']);
Route::get('/app/map', [ApiMapController::class, 'appMap']);

<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Post;
use App\Models\Category;

class HomeController extends Controller
{
    /**
     * Display the homepage.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        // Get latest posts for homepage
        $latestPosts = Post::with('category')
            ->latest()
            ->take(6)
            ->get();

        // Get all categories
        $categories = Category::withCount('posts')
            ->orderBy('posts_count', 'desc')
            ->take(10)
            ->get();

        return view('home', compact('latestPosts', 'categories'));
    }
}

<x-guest-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ trans_db('profile.edit_personal_info') ?? 'Chỉnh sửa thông tin cá nhân' }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="flex min-h-[400px]">
                    <!-- Sidebar -->
                    @include('dashboard.sidebar')

                    <!-- Main Content -->
                    <div class="w-3/4 p-6">
                        @if(session('success'))
                            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                                {{ session('success') }}
                            </div>
                        @endif

                        @if(session('error'))
                            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                                {{ session('error') }}
                            </div>
                        @endif

                        @if($errors->any())
                            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                                <ul class="list-disc list-inside">
                                    @foreach($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif

                        <h3 class="text-xl font-bold mb-6 text-gray-800">{{ trans_db('account.update_info') ?? 'Cập nhật thông tin tài khoản' }}</h3>
                        <form action="{{ route('dashboard.update', ['locale' => app()->getLocale()]) }}" method="POST" enctype="multipart/form-data" class="space-y-6 max-w-2xl" id="profile-update-form">
                            @csrf

                            <!-- Avatar Section -->
                            <div class="flex items-center gap-6">
                                <div class="flex-shrink-0">
                                    <img id="avatar-preview"
                                         src="{{ $account->avatar ? asset('storage/'.$account->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($account->full_name) }}"
                                         class="w-24 h-24 rounded-full object-cover border-2 border-gray-200"
                                         alt="Avatar">
                                </div>
                                <div class="flex-1">
                                    <label class="block font-semibold text-gray-700 mb-2">{{ trans_db('general.anh_dai_dien') ?? 'Ảnh đại diện' }}</label>
                                    <input type="file"
                                           name="avatar"
                                           id="avatar"
                                           accept="image/*"
                                           class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-[#10B981]/10 file:text-[#10B981] hover:file:bg-[#10B981]/20" />
                                    <p class="text-sm text-gray-500 mt-1">{{ trans_db('common.chon_anh_jpg_png_gif_toi_da_2mb') ?? 'Chọn ảnh JPG, PNG, GIF tối đa 2MB' }}</p>
                                </div>
                            </div>

                            <!-- Full Name -->
                            <div>
                                <label for="full_name" class="block font-semibold text-gray-700 mb-2">{{ trans_db('form.ho_va_ten') ?? 'Họ và tên' }}</label>
                                <input type="text"
                                       name="full_name"
                                       id="full_name"
                                       value="{{ old('full_name', $account->full_name) }}"
                                       class="w-full rounded-lg border border-gray-300 focus:border-[#10B981] focus:ring-[#10B981] px-4 py-3"
                                       required />
                            </div>

                            <!-- Email (Read-only) -->
                            <div>
                                <label for="email" class="block font-semibold text-gray-700 mb-2">{{ trans_db('form.email') ?? 'Email' }}</label>
                                <input type="email"
                                       id="email"
                                       value="{{ $account->email }}"
                                       class="w-full rounded-lg border border-gray-300 bg-gray-50 px-4 py-3"
                                       readonly />
                                <p class="text-sm text-gray-500 mt-1">{{ trans_db('form.email_khong_the_thay_doi') ?? 'Email không thể thay đổi' }}</p>
                            </div>

                            <!-- Phone Number -->
                            <div>
                                <label for="phone_number" class="block font-semibold text-gray-700 mb-2">{{ trans_db('form.so_dien_thoai') ?? 'Số điện thoại' }}</label>
                                <input type="text"
                                       name="phone_number"
                                       id="phone_number"
                                       value="{{ old('phone_number', $account->phone_number) }}"
                                       class="w-full rounded-lg border border-gray-300 focus:border-[#10B981] focus:ring-[#10B981] px-4 py-3"
                                       placeholder="{{ trans_db('form.nhap_so_dien_thoai') ?? 'Nhập số điện thoại' }}" />
                            </div>

                            <!-- Address -->
                            <div>
                                <label for="address" class="block font-semibold text-gray-700 mb-2">{{ trans_db('form.dia_chi') ?? 'Địa chỉ' }}</label>
                                <textarea name="address"
                                          id="address"
                                          rows="3"
                                          class="w-full rounded-lg border border-gray-300 focus:border-[#10B981] focus:ring-[#10B981] px-4 py-3"
                                          placeholder="{{ trans_db('form.nhap_dia_chi') ?? 'Nhập địa chỉ' }}">{{ old('address', $account->address) }}</textarea>
                            </div>

                            <!-- Submit Button -->
                            <div class="flex gap-4">
                                <button type="submit"
                                        class="py-3 px-8 rounded-lg text-white font-semibold bg-gradient-to-r from-[#10B981] to-[#0EA5E9] hover:from-[#0EA5E9] hover:to-[#10B981] transition">
                                    {{ trans_db('action.update_info') ?? 'Cập nhật thông tin' }}
                                </button>
                                <a href="{{ route('dashboard.profile', ['locale' => app()->getLocale()]) }}"
                                   class="py-3 px-8 rounded-lg text-gray-700 font-semibold border border-gray-300 hover:bg-gray-50 transition">
                                    {{ trans_db('form.huy') ?? 'Hủy bỏ' }}
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" />
    <script>
        // Preview avatar khi chọn file
        document.getElementById('avatar').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('avatar-preview').src = e.target.result;
                }
                reader.readAsDataURL(file);
            }
        });
    </script>
</x-guest-layout>

<header id="main-header" class="bg-white border-b border-red-200 sticky top-0 z-50 transition-shadow">
    <div class="container mx-auto flex items-center justify-between py-2 px-4">
        <div class="flex items-center space-x-2">
            <!-- Logo -->
            <div class="flex flex-col leading-tight">
                <a href="{{ localized_route('home') }}">
                    <span class="text-xl font-bold bg-gradient-to-r from-[#00c853] to-[#4a8af4] bg-clip-text text-transparent">EcoSolves</span>
                </a>
            </div>
        </div>
        <!-- Desktop Nav -->
        <nav class="hidden md:flex flex-1 justify-center space-x-8">
            <a href="{{ localized_route('home') }}#how-it-works" class="text-gray-700 hover:text-green-600" data-trans="nav.how_it_works">{{ trans_db('nav.how_it_works') }}</a>
            <a href="{{ localized_route('home') }}#communities" class="text-gray-700 hover:text-green-600" data-trans="nav.communities">{{ trans_db('nav.communities') }}</a>
            <a href="{{ localized_route('home') }}#experts" class="text-gray-700 hover:text-green-600" data-trans="nav.experts">{{ trans_db('nav.experts') }}</a>
            <a href="{{ localized_route('home') }}#about" class="text-gray-700 hover:text-green-600" data-trans="nav.about">{{ trans_db('nav.about') }}</a>
            <a href="{{ localized_route('pages.event') }}" class="text-gray-700 hover:text-green-600" data-trans="nav.events">{{ trans_db('nav.events') }}</a>
        </nav>
        <div class="hidden md:flex items-center space-x-4">
            <!-- Language Switcher -->
            <div class="mr-2">
                <x-lang-switcher style="minimal" />
            </div>
            @if (Auth::guard('account')->check() == false)
                <a href="{{ localized_route('login') }}" class="text-gray-700 hover:text-green-600" data-trans="nav.login">{{ trans_db('nav.login') }}</a>
                <a href="{{ localized_route('register') }}" class="px-4 py-2 rounded-md text-white font-semibold bg-gradient-to-r from-green-400 to-blue-400 hover:from-green-500 hover:to-blue-500 transition" data-trans="nav.register">{{ trans_db('nav.register') }}</a>
            @endif
            @if (Auth::guard('account')->check())
                <div class="relative group">
                    <button id="user-menu-btn" class="flex items-center space-x-2 focus:outline-none">
                        <img src="{{ Auth::guard('account')->user()->avatar ? asset('storage/' . Auth::guard('account')->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(Auth::guard('account')->user()->name ?? Auth::guard('account')->user()->email) . '&background=10B981&color=fff&size=32' }}" alt="Avatar" class="w-8 h-8 rounded-full border border-[#10B981] object-cover">
                        <span class="font-semibold text-[#10B981]">{{ Auth::guard('account')->user()->name ?? Auth::guard('account')->user()->email }}</span>
                        <svg class="w-4 h-4 ml-1 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <div id="user-dropdown" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-2 z-50 hidden group-focus:block group-hover:block">
                        <a href="{{ route('dashboard', ['locale' => app()->getLocale()]) }}" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">{{ trans_db('nav.profile') }}</a>
                        <form method="POST" action="{{ route('logout', ['locale' => app()->getLocale()]) }}">
                            @csrf
                            <button type="submit" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 w-full text-left">{{ trans_db('nav.logout') }}</button>
                        </form>
                    </div>
                </div>
            @endif
        </div>
        <!-- Hamburger for mobile -->
        <div class="md:hidden flex items-center relative">
            <button id="mobile-menu-toggle" aria-label="Open Menu" class="focus:outline-none">
                <svg id="icon-menu" class="h-7 w-7 text-gray-700 block" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                </svg>
                <svg id="icon-close" class="h-7 w-7 text-gray-700 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
            <!-- Mobile menu -->
            <div id="mobile-menu" class="fixed top-[45px] left-0 w-full bg-white shadow-lg z-50 hidden">
                <nav class="flex flex-col space-y-2 py-4 px-6">
                    <!-- Language Switcher Mobile -->
                    <div class="mb-2">
                        <x-lang-switcher style="select" />
                    </div>
                    <a href="{{ localized_route('home') }}#how-it-works" class="text-gray-700 hover:text-green-600 py-2">{{ trans_db('nav.how_it_works') }}</a>
                    <a href="{{ localized_route('home') }}#communities" class="text-gray-700 hover:text-green-600 py-2">{{ trans_db('nav.communities') }}</a>
                    <a href="{{ localized_route('home') }}#experts" class="text-gray-700 hover:text-green-600 py-2">{{ trans_db('nav.experts') }}</a>
                    <a href="{{ localized_route('home') }}#about" class="text-gray-700 hover:text-green-600 py-2">{{ trans_db('nav.about') }}</a>
                    <hr class="my-2">
                    @if (Auth::guard('account')->check() == false)
                        <a href="{{ localized_route('login') }}" class="text-gray-700 hover:text-green-600 py-2">{{ trans_db('nav.login') }}</a>
                        <a href="{{ localized_route('register') }}" class="px-4 py-2 rounded-md text-white font-semibold bg-gradient-to-r from-green-400 to-blue-400 hover:from-green-500 hover:to-blue-500 transition my-2 text-center">{{ trans_db('nav.register') }}</a>
                    @endif
                    @if (Auth::guard('account')->check())
                        <div class="relative group">
                            <button id="user-menu-btn-mobile" class="flex items-center space-x-2 focus:outline-none w-full py-2">
                                <img src="{{ Auth::guard('account')->user()->avatar ? asset('storage/' . Auth::guard('account')->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(Auth::guard('account')->user()->name ?? Auth::guard('account')->user()->email) . '&background=10B981&color=fff&size=32' }}" alt="Avatar" class="w-8 h-8 rounded-full border border-[#10B981] object-cover">
                                <span class="font-semibold text-[#10B981]">{{ Auth::guard('account')->user()->name ?? Auth::guard('account')->user()->email }}</span>
                                <svg class="w-4 h-4 ml-1 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>
                            <div id="user-dropdown-mobile" class="absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-2 z-50 hidden group-focus:block group-hover:block">
                                <a href="{{ route('dashboard', ['locale' => app()->getLocale()]) }}" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">{{ trans_db('nav.profile') }}</a>
                                <form method="POST" action="{{ route('logout', ['locale' => app()->getLocale()]) }}">
                                    @csrf
                                    <button type="submit" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 w-full text-left">{{ trans_db('nav.logout') }}</button>
                                </form>
                            </div>
                        </div>
                    @endif
                </nav>
            </div>
        </div>
    </div>
</header>

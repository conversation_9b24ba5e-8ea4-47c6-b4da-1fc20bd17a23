@extends('layouts.app')

@section('title', $category->name ?? 'Chi tiết danh mục')

@section('header', $category->name ?? 'Chi tiết danh mục')

@section('content')
    @if($category->description)
        <div class="mb-6 bg-gray-50 rounded-lg p-4">
            <p class="text-gray-700">{{ $category->description }}</p>
        </div>
    @endif

    <h2 class="text-xl font-semibold mb-4">{{ trans_db('general.bai_viet_trong_danh_muc_nayh2_div_class') }}</h2>

    <div class="space-y-6">
        @forelse($posts ?? [] as $post)
            <div class="border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                <div class="md:flex">
                    @if($post->featured_image)
                        <div class="md:flex-shrink-0">
                            <img src="{{ $post->featured_image }}" alt="{{ $post->title }}" class="h-48 w-full md:w-48 object-cover">
                        </div>
                    @endif
                    <div class="p-4">
                        <h3 class="text-lg font-semibold mb-2">
                            <a href="{{ route('posts.show', $post) }}" class="text-indigo-600 hover:text-indigo-800">{{ $post->title }}</a>
                        </h3>
                        <div class="text-sm text-gray-500 mb-2">
                            <span>{{ $post->created_at->format('d/m/Y') }}</span>
                        </div>
                        <p class="text-gray-600 mb-3">{{ Str::limit($post->excerpt, 150) }}</p>
                        <a href="{{ route('posts.show', $post) }}" class="text-sm text-indigo-600 hover:text-indigo-800 font-medium">{{ trans_db('general.doc_tiep') }}</a>
                    </div>
                </div>
            </div>
        @empty
            <div class="bg-gray-50 rounded-lg p-6 text-center">
                <p class="text-gray-500">{{ trans_db('common.chua_co_bai_viet_nao_trong_danh_muc_nay') }}</p>
            </div>
        @endforelse
    </div>

    @if(isset($posts) && $posts->hasPages())
        <div class="mt-6">
            {{ $posts->links() }}
        </div>
    @endif

    <div class="mt-6">
        <a href="{{ route('categories.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest hover:bg-gray-300 active:bg-gray-400 focus:outline-none focus:border-gray-400 focus:ring ring-gray-200 disabled:opacity-25 transition ease-in-out duration-150">
            {{ trans_db('general.quay_lai_danh_sach_danh_muc') }}
        </a>
    </div>
@endsection

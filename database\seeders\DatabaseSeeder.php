<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Category;
use App\Models\Post;
use App\Models\Tag;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create admin user
        User::factory()->create([
            'name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('DcRGlUxitcNt5uqK')
        ]);

        // Tạo mẫu 3 tài khoản account
        \App\Models\Account::factory(3)->create();

        // Tạo mẫu 10 điểm report
        \App\Models\Report::factory(10)->create();

        // Tạo mẫu 10 sự kiện tương ứng
        \App\Models\Event::factory(10)->create();

        $this->call([
            GiftSeeder::class,
            LanguageSeeder::class,
            TranslationSeeder::class
        ]);
    }
}

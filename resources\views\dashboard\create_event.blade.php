<x-guest-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ trans_db('action.tao_su_kien_moi') ?? 'Tạo sự kiện mới' }}
        </h2>
    </x-slot>

    <div class="max-w-2xl mx-auto py-10">
        <div class="bg-white shadow rounded-lg p-8">
            <h2 class="text-2xl font-bold mb-6 text-gray-800">{{ trans_db('action.tao_su_kien_moi') ?? 'Tạo sự kiện mới' }}</h2>
            @if (session('error'))
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    {{ session('error') }}
                </div>
            @endif
            <form action="{{ route('dashboard.events.store', ['locale' => app()->getLocale()]) }}" method="POST" enctype="multipart/form-data">
                @csrf
                <div class="mb-4">
                    <label for="title" class="block text-sm font-medium text-gray-700">
                        {{ trans_db('form.ten_su_kien') ?? 'Tên sự kiện' }} <span class="text-red-500">*</span>
                    </label>
                    <input type="text" name="title" id="title"
                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                        required value="{{ old('title') }}">
                    @error('title')
                        <span class="text-red-500 text-xs">{{ $message }}</span>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="description" class="block text-sm font-medium text-gray-700">{{ trans_db('general.mo_ta_ngan') ?? 'Mô tả ngắn' }}</label>
                    <textarea name="description" id="description" rows="2"
                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500">{{ old('description') }}</textarea>
                    @error('description')
                        <span class="text-red-500 text-xs">{{ $message }}</span>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="content" class="block text-sm font-medium text-gray-700">{{ trans_db('general.noi_dung_chi_tiet') ?? 'Nội dung chi tiết' }}</label>
                    <textarea name="content" id="content" rows="4"
                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500">{{ old('content') }}</textarea>
                    @error('content')
                        <span class="text-red-500 text-xs">{{ $message }}</span>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="location" class="block text-sm font-medium text-gray-700">{{ trans_db('general.dia_diem') ?? 'Địa điểm' }}</label>
                    <input type="text" name="location" id="location"
                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                        value="{{ old('location') }}">
                    @error('location')
                        <span class="text-red-500 text-xs">{{ $message }}</span>
                    @enderror
                </div>
                <div class="mb-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="start_date" class="block text-sm font-medium text-gray-700">
                            {{ trans_db('time.ngay_bat_dau') ?? 'Ngày bắt đầu' }} <span class="text-red-500">*</span>
                        </label>
                        <input type="datetime-local" name="start_date" id="start_date"
                            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                            required value="{{ old('start_date') }}">
                        @error('start_date')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                        @enderror
                    </div>
                    <div>
                        <label for="end_date" class="block text-sm font-medium text-gray-700">{{ trans_db('time.ngay_ket_thuc') ?? 'Ngày kết thúc' }}</label>
                        <input type="datetime-local" name="end_date" id="end_date"
                            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                            value="{{ old('end_date') }}">
                        @error('end_date')
                            <span class="text-red-500 text-xs">{{ $message }}</span>
                        @enderror
                    </div>
                </div>
                <div class="mb-4">
                    <label for="featured_image" class="block text-sm font-medium text-gray-700">{{ trans_db('general.anh_dai_dien') ?? 'Ảnh đại diện' }}</label>
                    <input type="file" name="featured_image" id="featured_image"
                        class="mt-1 block w-full text-sm text-gray-600">
                    @error('featured_image')
                        <span class="text-red-500 text-xs">{{ $message }}</span>
                    @enderror
                </div>
                <div class="mb-4">
                    <label for="max_participants" class="block text-sm font-medium text-gray-700">{{ trans_db('general.so_luong_tham_gia_toi_da') ?? 'Số lượng tham gia tối đa' }}</label>
                    <input type="number" name="max_participants" id="max_participants" min="1"
                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"
                        value="{{ old('max_participants') }}">
                    @error('max_participants')
                        <span class="text-red-500 text-xs">{{ $message }}</span>
                    @enderror
                </div>
                <div class="flex justify-end">
                    <a href="{{ route('dashboard', ['locale' => app()->getLocale()]) }}" class="px-4 py-2 bg-gray-200 text-gray-700 rounded mr-2">{{ trans_db('form.huy') ?? 'Hủy' }}</a>
                    <button type="submit" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700">{{ trans_db('action.tao_su_kien') ?? 'Tạo sự kiện' }}</button>
                </div>
            </form>
        </div>
    </div>
</x-guest-layout>

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('event_registrations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('event_id')->constrained('events')->onDelete('cascade');
            $table->foreignId('account_id')->constrained('accounts')->onDelete('cascade');
            $table->timestamp('registered_at')->useCurrent();
            $table->string('note')->nullable();
            $table->timestamps();
            $table->unique(['event_id', 'account_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('event_registrations');
    }
}; 
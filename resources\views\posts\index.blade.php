@extends('layouts.app')

@section('title', trans_db('general.bai_viet'))

@section('header', trans_db('general.tat_ca_bai_viet'))

@section('content')
    @if(isset($featuredPost))
        <div class="mb-8 border rounded-lg overflow-hidden shadow-md:flex-shrink-0 md:w-1/3">
                        <img src="{{ $featuredPost->featured_image }}" alt="{{ $featuredPost->title }}" class="h-64 w-full object-cover-2/3">
                    <div class="uppercase tracking-wide text-sm text-indigo-600 font-semibold mb-1">{{ trans_db('general.bai_viet_noi_batdiv_h2_class') }}
                        <a href="{{ route('posts.show', $featuredPost) }}" class="text-gray-900 hover:text-indigo-600">{{ $featuredPost->title }}</a>
                    </h2>
                    <div class="text-sm text-gray-500 mb-3">
                        <span>{{ $featuredPost->created_at->format('d/m/Y') }}</span>
                        @if($featuredPost->category)
                            <span class="mx-1">•</span>
                            <a href="{{ route('categories.show', $featuredPost->category) }}" class="text-indigo-500 hover:text-indigo-700">{{ $featuredPost->category->name }}</a>
                        @endif
                    </div>
                    <p class="text-gray-600 mb-4">{{ Str::limit($featuredPost->excerpt, 200) }}</p>
                    <a href="{{ route('posts.show', $featuredPost) }}" class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 active:bg-indigo-800 focus:outline-none focus:border-indigo-800 focus:ring ring-indigo-300 disabled:opacity-25 transition ease-in-out duration-150">
                        {{ trans_db('general.doc_tiep') }}
                    </a>
                </div>
            </div>
        </div>
    @endif

    <div class="mb-6 flex justify-between items-center">
        <h2 class="text-xl font-semibold">{{ trans_db('general.bai_viet_moi_nhat') }}</h2>

        @if(isset($categories) && count($categories) > 0)
            <div class="relative">
                <select onchange="window.location = this.value" class="block appearance-none bg-white border border-gray-300 hover:border-gray-400 px-4 py-2 pr-8 rounded shadow leading-tight focus:outline-none focus:shadow-outline">
                    <option value="{{ route('posts.index') }}">{{ trans_db('common.tat_ca_danh_muc') }}</option>
                    @foreach($categories as $category)
                        <option value="{{ route('categories.show', $category) }}" {{ request()->is('categories/'.$category->id) ? 'selected' : '' }}>
                            {{ $category->name }}
                        </option>
                    @endforeach
                </select>
                <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                    <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                        <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/>
                    </svg>
                </div>
            </div>
        @endif
    </div>

    <div class="space-y-6">
        @forelse($posts ?? [] as $post)
            <div class="border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                <div class="md:flex">
                    @if($post->featured_image)
                        <div class="md:flex-shrink-0">
                            <img src="{{ $post->featured_image }}" alt="{{ $post->title }}" class="h-48 w-full md:w-48 object-cover">
                        </div>
                    @endif
                    <div class="p-4">
                        <h3 class="text-lg font-semibold mb-2">
                            <a href="{{ route('posts.show', $post) }}" class="text-indigo-600 hover:text-indigo-800">{{ $post->title }}</a>
                        </h3>
                        <div class="text-sm text-gray-500 mb-2">
                            <span>{{ $post->created_at->format('d/m/Y') }}</span>
                            @if($post->category)
                                <span class="mx-1">•</span>
                                <a href="{{ route('categories.show', $post->category) }}" class="text-indigo-500 hover:text-indigo-700">{{ $post->category->name }}</a>
                            @endif
                        </div>
                        <p class="text-gray-600 mb-3">{{ Str::limit($post->excerpt, 150) }}</p>
                        <a href="{{ route('posts.show', $post) }}" class="text-sm text-indigo-600 hover:text-indigo-800 font-medium">{{ trans_db('general.doc_tiep') }} →</a>
                    </div>
                </div>
            </div>
        @empty
            <div class="bg-gray-50 rounded-lg p-6 text-center">
                <p class="text-gray-500">{{ trans_db('common.chua_co_bai_viet_nao') }}</p>
            </div>
        @endforelse
    </div>

    @if(isset($posts) && $posts->hasPages())
        <div class="mt-6">
            {{ $posts->links() }}
        </div>
    @endif
@endsection

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Thread extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'content',
        'account_id',
        'views',
        'replies_count',
    ];

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function replies()
    {
        return $this->hasMany(Reply::class);
    }

    public function getAuthorAttribute()
    {
        return $this->account;
    }
} 
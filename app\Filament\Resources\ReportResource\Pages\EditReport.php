<?php

namespace App\Filament\Resources\ReportResource\Pages;

use App\Filament\Resources\ReportResource;
use App\Models\PointHistory;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditReport extends EditRecord
{
    protected static string $resource = ReportResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function afterSave(): void
    {
        $record = $this->record;
        
        // Kiểm tra nếu status được cập nhật thành 'approved'
        if ($record->wasChanged('status') && $record->status === 'approved') {
            $accountId = $record->created_by;
            $description = $record->description;
            
            // Kiểm tra xem đã có bản ghi PointHistory nào với account_id và description này chưa
            $existingPointHistory = PointHistory::where('account_id', $accountId)
                ->where('description', 'Cộng điểm khi báo cáo được xác nhận: ' . $description)
                ->first();
            
            if (!$existingPointHistory) {
                // Cộng 10 điểm cho account
                $account = $record->creator;
                $account->point += 10;
                $account->save();
                
                // Tạo bản ghi lịch sử tích điểm
                PointHistory::create([
                    'account_id' => $accountId,
                    'change' => 10,
                    'description' => 'Cộng điểm khi báo cáo được xác nhận: ' . $description,
                ]);
            }
        }
    }
}

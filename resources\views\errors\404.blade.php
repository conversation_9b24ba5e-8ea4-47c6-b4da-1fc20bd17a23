@extends('layouts.app')

@section('title', trans_db('general.khong_tim_thay_tran'))

@section('header', trans('status.loi_404_khong_tim_thay_trang'))

@section('content')
    <div class="text-center py-8">
                <div class="mb-6">
                    <svg class="w-24 h-24 text-indigo-500 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>

                <h2 class="text-3xl font-bold text-gray-800 mb-4">{{ trans_db('action.rat_tiec_trang_ban_dang_tim_kiem_khong_ton_tai') }}</h2>

                <p class="text-gray-600 mb-8 max-w-md mx-auto">
                    {{ trans_db('404.trang_da_bi_xoa_tam_thoi_khong_kha_dung') }}.
                    {{ trans_db('404.vui_long_kiem_tra_duong_dan') }}.
                </p>

                <div class="space-y-4">
                    <a href="{{ localized_route('home') }}" class="inline-flex items-center px-6 py-3 bg-indigo-600 border border-transparent rounded-md font-semibold text-sm text-white uppercase tracking-widest hover:bg-indigo-700 active:bg-indigo-800 focus:outline-none focus:border-indigo-800 focus:ring ring-indigo-300 disabled:opacity-25 transition ease-in-out duration-150">
                        {{ trans_db('nav.quay_lai_trang_chu') }}
                    </a>

                    <div>
                        <a href="javascript:history.back()" class="text-indigo-600 hover:text-indigo-800 font-medium">
                            {{ trans_db('general.quay_lai_trang_truoc') }}
                        </a>
                    </div>
                </div>

                <div class="mt-12">
                    <h3 class="text-lg font-semibold mb-4">{{ trans_db('common.ban_co_the_quan_tam_den') }}</h3>

                    <div class="flex flex-wrap justify-center gap-4">
                        <a href="{{ route('posts.index') }}" class="px-4 py-2 bg-gray-100 rounded-md text-gray-700 hover:bg-gray-200 transition-colors">
                            {{ trans_db('general.bai_viet_moi_nhat') }}
                        </a>
                        <a href="{{ route('categories.index') }}" class="px-4 py-2 bg-gray-100 rounded-md text-gray-700 hover:bg-gray-200 transition-colors">
                            {{ trans_db('general.danh_muc') }}
                        </a>
                        <a href="{{ route('pages.about') }}" class="px-4 py-2 bg-gray-100 rounded-md text-gray-700 hover:bg-gray-200 transition-colors">
                            {{ trans_db('nav.gioi_thieu') }}
                        </a>
                        <a href="{{ localized_route('posts.index') }}" class="px-4 py-2 bg-gray-100 rounded-md text-gray-700 hover:bg-gray-200 transition-colors">
                            {{ trans_db('nav.contact') }}
                        </a>
                    </div>
                </div>
        </div>
@endsection

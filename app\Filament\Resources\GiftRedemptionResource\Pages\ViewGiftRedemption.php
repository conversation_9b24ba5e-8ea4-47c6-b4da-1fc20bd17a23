<?php

namespace App\Filament\Resources\GiftRedemptionResource\Pages;

use App\Filament\Resources\GiftRedemptionResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewGiftRedemption extends ViewRecord
{
    protected static string $resource = GiftRedemptionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
} 
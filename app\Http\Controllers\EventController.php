<?php

namespace App\Http\Controllers;

use App\Models\Event;

class EventController extends Controller
{
    public function __construct() {}

    public function event()
    {
        $events = Event::where('status', 'approved')
            ->where(function ($q) {
                $q->whereNull('end_date')->orWhere('end_date', '>=', now());
            })
            ->orderBy('start_date', 'desc')
            ->get();
        return view('pages.event', compact('events'));
    }

    public function show($slug)
    {
        $event = Event::where('slug', $slug)->firstOrFail();
        $accountId = auth('account')->id();
        $registered = false;
        if ($accountId) {
            $registered = \App\Models\EventRegistration::where('event_id', $event->id)
                ->where('account_id', $accountId)
                ->exists();
        }

        return view('pages.event-show', compact('event', 'registered'));
    }
}

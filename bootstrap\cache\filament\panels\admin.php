<?php return array (
  'livewireComponents' => 
  array (
    'app.filament.resources.account-resource.pages.create-account' => 'App\\Filament\\Resources\\AccountResource\\Pages\\CreateAccount',
    'app.filament.resources.account-resource.pages.edit-account' => 'App\\Filament\\Resources\\AccountResource\\Pages\\EditAccount',
    'app.filament.resources.account-resource.pages.list-accounts' => 'App\\Filament\\Resources\\AccountResource\\Pages\\ListAccounts',
    'app.filament.resources.category-resource.pages.create-category' => 'App\\Filament\\Resources\\CategoryResource\\Pages\\CreateCategory',
    'app.filament.resources.category-resource.pages.edit-category' => 'App\\Filament\\Resources\\CategoryResource\\Pages\\EditCategory',
    'app.filament.resources.category-resource.pages.list-categories' => 'App\\Filament\\Resources\\CategoryResource\\Pages\\ListCategories',
    'app.filament.resources.event-resource.pages.create-event' => 'App\\Filament\\Resources\\EventResource\\Pages\\CreateEvent',
    'app.filament.resources.event-resource.pages.edit-event' => 'App\\Filament\\Resources\\EventResource\\Pages\\EditEvent',
    'app.filament.resources.event-resource.pages.list-events' => 'App\\Filament\\Resources\\EventResource\\Pages\\ListEvents',
    'app.filament.resources.gift-redemption-resource.pages.create-gift-redemption' => 'App\\Filament\\Resources\\GiftRedemptionResource\\Pages\\CreateGiftRedemption',
    'app.filament.resources.gift-redemption-resource.pages.edit-gift-redemption' => 'App\\Filament\\Resources\\GiftRedemptionResource\\Pages\\EditGiftRedemption',
    'app.filament.resources.gift-redemption-resource.pages.list-gift-redemptions' => 'App\\Filament\\Resources\\GiftRedemptionResource\\Pages\\ListGiftRedemptions',
    'app.filament.resources.gift-redemption-resource.pages.view-gift-redemption' => 'App\\Filament\\Resources\\GiftRedemptionResource\\Pages\\ViewGiftRedemption',
    'app.filament.resources.gift-resource.pages.create-gift' => 'App\\Filament\\Resources\\GiftResource\\Pages\\CreateGift',
    'app.filament.resources.gift-resource.pages.edit-gift' => 'App\\Filament\\Resources\\GiftResource\\Pages\\EditGift',
    'app.filament.resources.gift-resource.pages.list-gifts' => 'App\\Filament\\Resources\\GiftResource\\Pages\\ListGifts',
    'app.filament.resources.language-resource.pages.create-language' => 'App\\Filament\\Resources\\LanguageResource\\Pages\\CreateLanguage',
    'app.filament.resources.language-resource.pages.edit-language' => 'App\\Filament\\Resources\\LanguageResource\\Pages\\EditLanguage',
    'app.filament.resources.language-resource.pages.list-languages' => 'App\\Filament\\Resources\\LanguageResource\\Pages\\ListLanguages',
    'app.filament.resources.message-resource.pages.create-message' => 'App\\Filament\\Resources\\MessageResource\\Pages\\CreateMessage',
    'app.filament.resources.message-resource.pages.edit-message' => 'App\\Filament\\Resources\\MessageResource\\Pages\\EditMessage',
    'app.filament.resources.message-resource.pages.list-messages' => 'App\\Filament\\Resources\\MessageResource\\Pages\\ListMessages',
    'app.filament.resources.post-resource.pages.create-post' => 'App\\Filament\\Resources\\PostResource\\Pages\\CreatePost',
    'app.filament.resources.post-resource.pages.edit-post' => 'App\\Filament\\Resources\\PostResource\\Pages\\EditPost',
    'app.filament.resources.post-resource.pages.list-posts' => 'App\\Filament\\Resources\\PostResource\\Pages\\ListPosts',
    'app.filament.resources.report-resource.pages.create-report' => 'App\\Filament\\Resources\\ReportResource\\Pages\\CreateReport',
    'app.filament.resources.report-resource.pages.edit-report' => 'App\\Filament\\Resources\\ReportResource\\Pages\\EditReport',
    'app.filament.resources.report-resource.pages.list-reports' => 'App\\Filament\\Resources\\ReportResource\\Pages\\ListReports',
    'app.filament.resources.tag-resource.pages.create-tag' => 'App\\Filament\\Resources\\TagResource\\Pages\\CreateTag',
    'app.filament.resources.tag-resource.pages.edit-tag' => 'App\\Filament\\Resources\\TagResource\\Pages\\EditTag',
    'app.filament.resources.tag-resource.pages.list-tags' => 'App\\Filament\\Resources\\TagResource\\Pages\\ListTags',
    'app.filament.resources.translation-resource.pages.create-translation' => 'App\\Filament\\Resources\\TranslationResource\\Pages\\CreateTranslation',
    'app.filament.resources.translation-resource.pages.edit-translation' => 'App\\Filament\\Resources\\TranslationResource\\Pages\\EditTranslation',
    'app.filament.resources.translation-resource.pages.list-translations' => 'App\\Filament\\Resources\\TranslationResource\\Pages\\ListTranslations',
    'app.filament.resources.user-resource.pages.create-user' => 'App\\Filament\\Resources\\UserResource\\Pages\\CreateUser',
    'app.filament.resources.user-resource.pages.edit-user' => 'App\\Filament\\Resources\\UserResource\\Pages\\EditUser',
    'app.filament.resources.user-resource.pages.list-users' => 'App\\Filament\\Resources\\UserResource\\Pages\\ListUsers',
    'app.filament.resources.yes-resource.pages.settings' => 'App\\Filament\\Resources\\YesResource\\Pages\\Settings',
    'app.filament.pages.settings' => 'App\\Filament\\Pages\\Settings',
    'filament.pages.dashboard' => 'Filament\\Pages\\Dashboard',
    'app.filament.widgets.latest-posts' => 'App\\Filament\\Widgets\\LatestPosts',
    'app.filament.widgets.posts-chart' => 'App\\Filament\\Widgets\\PostsChart',
    'app.filament.widgets.stats-overview' => 'App\\Filament\\Widgets\\StatsOverview',
    'filament.widgets.account-widget' => 'Filament\\Widgets\\AccountWidget',
    'filament.livewire.database-notifications' => 'Filament\\Livewire\\DatabaseNotifications',
    'filament.pages.auth.edit-profile' => 'Filament\\Pages\\Auth\\EditProfile',
    'filament.livewire.global-search' => 'Filament\\Livewire\\GlobalSearch',
    'filament.livewire.notifications' => 'Filament\\Livewire\\Notifications',
    'filament.pages.auth.login' => 'Filament\\Pages\\Auth\\Login',
    'filament.resources.relation-managers.relation-manager' => 'Filament\\Resources\\RelationManagers\\RelationManager',
  ),
  'clusters' => 
  array (
  ),
  'clusteredComponents' => 
  array (
  ),
  'clusterDirectories' => 
  array (
  ),
  'clusterNamespaces' => 
  array (
  ),
  'pages' => 
  array (
    'F:\\backend-app_don_rac\\app\\Filament\\Pages\\Settings.php' => 'App\\Filament\\Pages\\Settings',
    0 => 'Filament\\Pages\\Dashboard',
    1 => 'App\\Filament\\Pages\\Settings',
  ),
  'pageDirectories' => 
  array (
    0 => 'F:\\backend-app_don_rac\\app\\Filament/Pages',
  ),
  'pageNamespaces' => 
  array (
    0 => 'App\\Filament\\Pages',
  ),
  'resources' => 
  array (
    'F:\\backend-app_don_rac\\app\\Filament\\Resources\\AccountResource.php' => 'App\\Filament\\Resources\\AccountResource',
    'F:\\backend-app_don_rac\\app\\Filament\\Resources\\CategoryResource.php' => 'App\\Filament\\Resources\\CategoryResource',
    'F:\\backend-app_don_rac\\app\\Filament\\Resources\\EventResource.php' => 'App\\Filament\\Resources\\EventResource',
    'F:\\backend-app_don_rac\\app\\Filament\\Resources\\GiftRedemptionResource.php' => 'App\\Filament\\Resources\\GiftRedemptionResource',
    'F:\\backend-app_don_rac\\app\\Filament\\Resources\\GiftResource.php' => 'App\\Filament\\Resources\\GiftResource',
    'F:\\backend-app_don_rac\\app\\Filament\\Resources\\LanguageResource.php' => 'App\\Filament\\Resources\\LanguageResource',
    'F:\\backend-app_don_rac\\app\\Filament\\Resources\\MessageResource.php' => 'App\\Filament\\Resources\\MessageResource',
    'F:\\backend-app_don_rac\\app\\Filament\\Resources\\PostResource.php' => 'App\\Filament\\Resources\\PostResource',
    'F:\\backend-app_don_rac\\app\\Filament\\Resources\\ReportResource.php' => 'App\\Filament\\Resources\\ReportResource',
    'F:\\backend-app_don_rac\\app\\Filament\\Resources\\TagResource.php' => 'App\\Filament\\Resources\\TagResource',
    'F:\\backend-app_don_rac\\app\\Filament\\Resources\\TranslationResource.php' => 'App\\Filament\\Resources\\TranslationResource',
    'F:\\backend-app_don_rac\\app\\Filament\\Resources\\UserResource.php' => 'App\\Filament\\Resources\\UserResource',
  ),
  'resourceDirectories' => 
  array (
    0 => 'F:\\backend-app_don_rac\\app\\Filament/Resources',
  ),
  'resourceNamespaces' => 
  array (
    0 => 'App\\Filament\\Resources',
  ),
  'widgets' => 
  array (
    'F:\\backend-app_don_rac\\app\\Filament\\Widgets\\LatestPosts.php' => 'App\\Filament\\Widgets\\LatestPosts',
    'F:\\backend-app_don_rac\\app\\Filament\\Widgets\\PostsChart.php' => 'App\\Filament\\Widgets\\PostsChart',
    'F:\\backend-app_don_rac\\app\\Filament\\Widgets\\StatsOverview.php' => 'App\\Filament\\Widgets\\StatsOverview',
    0 => 'Filament\\Widgets\\AccountWidget',
    1 => 'App\\Filament\\Widgets\\StatsOverview',
    2 => 'App\\Filament\\Widgets\\PostsChart',
    3 => 'App\\Filament\\Widgets\\LatestPosts',
  ),
  'widgetDirectories' => 
  array (
    0 => 'F:\\backend-app_don_rac\\app\\Filament/Widgets',
  ),
  'widgetNamespaces' => 
  array (
    0 => 'App\\Filament\\Widgets',
  ),
);
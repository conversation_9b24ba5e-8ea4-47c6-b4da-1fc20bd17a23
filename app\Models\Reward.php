<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Reward extends Model
{
    protected $fillable = [
        'name',
        'description',
        'image',
        'point_cost',
        'quantity',
        'is_active',
    ];

    /**
     * Get the redemptions for the reward.
     */
    public function redemptions()
    {
        return $this->hasMany(RewardRedemption::class);
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class GiftRedemption extends Model
{
    protected $fillable = [
        'account_id',
        'gift_id',
        'quantity',
        'point_used',
        'status',
        'notes',
    ];

    /**
     * Get the account that owns the redemption.
     */
    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    /**
     * Get the gift that owns the redemption.
     */
    public function gift()
    {
        return $this->belongsTo(Gift::class);
    }
}

<?php

namespace Tests\Feature;

use App\Models\Account;
use App\Models\Language;
use App\Notifications\VerifyAccountEmail;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\URL;
use Tests\TestCase;

class AccountEmailVerificationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create default language
        Language::create([
            'locale' => 'vi',
            'name' => 'Tiếng Việt',
            'is_default' => true,
            'is_active' => true,
        ]);
        
        Language::create([
            'locale' => 'en',
            'name' => 'English',
            'is_default' => false,
            'is_active' => true,
        ]);
    }

    public function test_verification_notice_screen_can_be_rendered(): void
    {
        $account = Account::factory()->create(['email_verified_at' => null]);

        $response = $this->actingAs($account, 'account')
            ->get('/vi/dashboard/verification-notice');

        $response->assertStatus(200);
    }

    public function test_email_can_be_verified_with_locale(): void
    {
        $account = Account::factory()->create(['email_verified_at' => null]);

        $verificationUrl = URL::temporarySignedRoute(
            'verification.verify',
            now()->addMinutes(60),
            [
                'locale' => 'vi',
                'id' => $account->id,
                'hash' => sha1($account->email)
            ]
        );

        $response = $this->actingAs($account, 'account')->get($verificationUrl);

        $this->assertTrue($account->fresh()->hasVerifiedEmail());
        $response->assertRedirect(route('dashboard', ['locale' => 'vi']));
    }

    public function test_email_verification_notification_contains_locale(): void
    {
        Notification::fake();

        $account = Account::factory()->create(['email_verified_at' => null]);

        app()->setLocale('vi');
        $account->sendEmailVerificationNotification();

        Notification::assertSentTo($account, VerifyAccountEmail::class);
    }

    public function test_email_is_not_verified_with_invalid_hash(): void
    {
        $account = Account::factory()->create(['email_verified_at' => null]);

        $verificationUrl = URL::temporarySignedRoute(
            'verification.verify',
            now()->addMinutes(60),
            [
                'locale' => 'vi',
                'id' => $account->id,
                'hash' => sha1('wrong-email')
            ]
        );

        $this->actingAs($account, 'account')->get($verificationUrl);

        $this->assertFalse($account->fresh()->hasVerifiedEmail());
    }

    public function test_verification_send_route_works_with_locale(): void
    {
        Notification::fake();

        $account = Account::factory()->create(['email_verified_at' => null]);

        $response = $this->actingAs($account, 'account')
            ->post('/vi/email/verification-notification');

        $response->assertRedirect();
        $response->assertSessionHas('status', 'verification-link-sent');

        Notification::assertSentTo($account, VerifyAccountEmail::class);
    }
}

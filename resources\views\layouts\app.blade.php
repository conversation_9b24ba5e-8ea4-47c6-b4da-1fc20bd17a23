<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="locale" content="{{ app()->getLocale() }}">

    <!-- SEO Meta -->
    <meta name="robots" content="noindex, nofollow">
    <meta name="description" content="{{ trans_db('action.ecosolves_nen_tang_cong_dong_kien_tao_giai_phap_mo') }}">
    <meta name="keywords" content="{{ trans_db('general.moi_truong_cong_dong_giai_phap_ben_vung_ecosolves_') }}">
    <meta name="author" content="EcoSolves Team">
    <meta name="theme-color" content="#10B981">
    <!-- Open Graph -->
    <meta property="og:title" content="{{ trans_db('general.ecosolves_nen_tang_cong_dong_moi_truong') }}">
    <meta property="og:description"
        content="{{ trans_db('action.tham_gia_cong_dong_ecosolves_de_cung_kien_tao_giai') }}">
    <meta property="og:type" content="website">
    <meta property="og:image" content="/images/logo.png">
    <meta property="og:url" content="{{ url()->current() }}">
    <title>{{ trans_db('general.ecosolves_nen_tang_cong_dong_moi_truong') }}</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100..900;1,100..900&display=swap"
        rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" />
    <!-- Scripts -->
    @vite(['resources/css/app.css'])
</head>

<body class="font-sans antialiased">
    <div class="min-h-screen bg-gray-100">
        @component('components.header')
        @endcomponent
        <!-- Page Content -->
        <main>
            @yield('content')
        </main>
        @component('components.footer')
        @endcomponent
    </div>
    <!-- jQuery for mobile menu toggle -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    @vite(['resources/js/app.js'])


    <!-- jQuery script for mobile menu toggle -->
    <script>
        $(document).ready(function() {
            $('#user-menu-btn-mobile').on('click', function(e) {
                e.stopPropagation();
                $('#user-dropdown-mobile').toggleClass('hidden');
            });
            $(document).on('click', function(e) {
                if (!$(e.target).closest('#user-menu-btn-mobile, #user-dropdown-mobile').length) {
                    $('#user-dropdown-mobile').addClass('hidden');
                }
            });
            $('#user-menu-btn').on('click', function(e) {
                e.stopPropagation();
                $('#user-dropdown').toggleClass('hidden');
            });
            $(document).on('click', function(e) {
                if (!$(e.target).closest('#user-menu-btn, #user-dropdown').length) {
                    $('#user-dropdown').addClass('hidden');
                }
            });

            $('#mobile-menu-toggle').on('click', function(e) {
                e.stopPropagation();
                $('#mobile-menu').toggleClass('hidden');
                $('#icon-menu').toggleClass('hidden');
                $('#icon-close').toggleClass('hidden"{{ trans_db('nav.dong_menu_khi_click_ra_ngoai_documenton') }}"click', function(e) {
                if (!$(e.target).closest('#mobile-menu, #mobile-menu-toggle').length) {
                    $('#mobile-menu').addClass('hidden');
                    $('#icon-menu').removeClass('hidden');
                    $('#icon-close').addClass('hidden');
                }
            });

            // Sticky header shadow on scroll
            $(window).on('scroll', function() {
                if ($(window).scrollTop() > 10) {
                    $('#main-header').addClass('shadow');
                } else {
                    $('#main-header').removeClass('shadow');
                }
            });

            // Tự động lấy vị trí GPS nếu trình duyệt cho phép
            function tryGetLocation() {
                var latInput = $('#latitude');
                var lngInput = $('#longitude');
                var status = $('#gps-status');
                var retryBtn = $('#gps-retry-btn');
                retryBtn.remove();
                if (navigator.geolocation) {
                    status.text('{{ trans_db('general.dang_lay_vi_tri') }}');
                    navigator.geolocation.getCurrentPosition(function(position) {
                        latInput.val(position.coords.latitude);
                        lngInput.val(position.coords.longitude);
                        status.text('{{ trans_db('form.da_tu_dong_dien_vi_tri_ban_co_the_chinh_sua_neu_ca') }}');
                        retryBtn.remove();
                    }, function(error) {
                        status.html(
                            '{{ trans_db('general.khong_the_tu_dong_lay_vi_tri_vui_long_kiem_tra_cai') }}'
                        );
                        if ($('#gps-retry-btn').length === 0) {
                            status.append(
                                '<br><button type="button" id="gps-retry-btn" class="mt-2 px-3 py-1 rounded bg-gradient-to-r from-[#10B981] to-[#0EA5E9] hover:from-[#0EA5E9] hover:to-[#10B981] text-white text-sm font-semibold hover:bg-[#0EA5E9] transition""{{ trans_db('general.thu_lai_lay_vi_tri') }}"/button>'
                            );
                        }
                    });
                } else {
                    status.text('{{ trans_db('form.trinh_duyet_khong_ho_tro_lay_vi_tri_vui_long_nhap') }}');
                }
            }
            $(function() {
                tryGetLocation();
                $(document).on('click', '#gps-retry-btn', function() {
                    tryGetLocation();
                });
            });
            $('#register-btn').on('click', function() {
                var btn = $(this);
                btn.prop('disabled', true);
                let event_id = btn.data('id');
                $.ajax({
                    url: "/api/event/register",
                    method: 'POST',
                    data: {
                        event_id: event_id,
                        _token: $('input[name=_token]').val()
                    },
                    success: function(data) {
                        if (data.success) {
                            toastr.success(data.message);
                            btn.text("{{ trans_db('form.da_dang_ky_tham_gia') }}")
                                .removeClass(
                                    'bg-gradient-to-r from-[#10B981] to-[#0EA5E9] hover:from-[#0EA5E9] hover:to-[#10B981]'
                                )
                                .addClass('bg-gray-400 cursor-not-allowed');
                        } else {
                            toastr.error(data.message);
                            btn.prop('disabled', false);
                        }
                    },
                    error: function() {
                        toastr.error("{{ trans_db('status.co_loi_xay_ra_vui_long_thu_lai') }}");
                        btn.prop('disabled', false);
                    }
                });
            });
            $('#create-thread-form').on('submit', function(e) {
                e.preventDefault();
                $('#error-title').text('');
                $('#error-content').text('');
                $.ajax({
                    url: "/api/forum/create",
                    type: "POST",
                    data: $(this).serialize(),
                    headers: {
                        'X-CSRF-TOKEN': "{{ csrf_token() }}"
                    },
                    success: function(res) {
                        toastr.success('{{ trans_db('status.dang_chu_de_thanh_cong') }}');
                        setTimeout(function() {
                            window.location.href = res.redirect ||
                                "{{ localized_route('forum.index') }}";
                        }, 1200);
                    },
                    error: function(xhr) {
                        if (xhr.status === 422) {
                            let errors = xhr.responseJSON.errors;
                            if (errors.title) $('#error-title').text(errors.title[0]);
                            if (errors.content) $('#error-content').text(errors.content[0]);
                        } else {
                            toastr.error('Có lỗi xảy ra, vui lòng thử lại!');
                        }
                    }
                });
            });
            $('#reply-form').on('submit', function(e) {
                e.preventDefault();
                let form = $(this);
                $.ajax({
                    url: '/api/forum/reply',
                    type: 'POST',
                    data: form.serialize(),
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    success: function(res) {
                        toastr.success(res.message || '{{ trans_db('form.gui_phan_hoi_thanh_cong') }}');
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    },
                    error: function(xhr) {
                        if (xhr.status === 422) {
                            let errors = xhr.responseJSON.errors;
                            toastr.error(errors.content ? errors.content[0] :
                                '{{ trans_db('form.vui_long_nhap_noi_dung') }}');
                        } else if (xhr.status === 401) {
                            toastr.error('{{ trans_db('form.ban_can_dang_nhap_de_gui_phan_hoi') }}');
                        } else {
                            toastr.error('Có lỗi xảy ra, vui lòng thử lại!');
                        }
                    }
                });
            });

            // Emoji dropup
            $('#emoji-btn').on('click', function(e) {
                e.preventDefault();
                $('#emoji-dropup').toggleClass('hidden"{{ trans_db('common.chon_emoji') }}"#emoji-dropup .emoji-item').on('click', function() {
                let emoji = $(this).text();
                let textarea = $('textarea[name="content"]');
                let start = textarea[0].selectionStart;
                let end = textarea[0].selectionEnd;
                let text = textarea.val();
                textarea.val(text.substring(0, start) + emoji + text.substring(end));
                textarea[0].focus();
                textarea[0].selectionStart = textarea[0].selectionEnd = start + emoji.length;
                $('#emoji-dropup').addClass('hidden"{{ trans_db('general.dong_dropup_khi_click_ngoai_documenton') }}"click', function(e) {
                if (!$(e.target).closest('#emoji-btn, #emoji-dropup').length) {
                    $('#emoji-dropup').addClass('hidden');
                }
            });
        });
    </script>
    <script>
        $(function() {
            // Theo dõi thay đổi trạng thái GPS để hiện ô nhập link nếu cần
            function showGmapLink(show) {
                if (show) {
                    $('#gmap-link-group').removeClass('hidden');
                } else {
                    $('#gmap-link-group').addClass('hidden"{{ trans_db('common.theo_doi_nut_thu_lai_va_trang_thai_gps_documenton') }}"click', '#gps-retry-btn', function() {
                showGmapLink(false);
            });
            // Theo dõi khi không lấy được GPS (bằng cách hook vào status thay đổi)
            const gpsStatusEl = document.getElementById('gps-status');
            if (gpsStatusEl) {
                const origStatusHtml = $('#gps-status').html();
                const observer = new MutationObserver(function(mutations) {
                    const text = $('#gps-status').text();
                    if (text.includes('{{ trans_db('general.khong_the_tu_dong_lay_vi_tri') }}') || text.includes(
                            '{{ trans_db('general.trinh_duyet_khong_ho_tro') }}')) {
                        showGmapLink(true);
                    } else {
                        showGmapLink(false);
                    }
                });
                observer.observe(gpsStatusEl, {
                    childList: true,
                    subtree: true
                });
            }
            // Tự động tách tọa độ từ link Google Maps
            $('#gmap-link').on('input', function() {
                const val = $(this).val();
                // Regex lấy lat/lng từ link Google Maps
                // Hỗ trợ dạng .../@lat,lng,... hoặc .../place/.../lat,lng...
                const regex = /@(-?\d+\.\d+),(-?\d+\.\d+)/;
                const match = val.match(regex);
                if (match) {
                    $('#latitude').val(match[1]);
                    $('#longitude"{{ trans_db('general.valmatch2_ajax_submit_form_bao_cao') }}"#report-form').on('submit', function(e) {
                e.preventDefault();
                var $btn = $(this);
                var gmapLink = $('#gmap-link').val().trim();
                if (gmapLink) {
                    // Regex lấy lat/lng từ link Google Maps
                    var regex = /@(-?\d+\.\d+),(-?\d+\.\d+)/;
                    var match = gmapLink.match(regex);
                    if (match) {
                        $('#latitude').val(match[1]);
                        $('#longitude').val(match[2]);
                    } else {
                        toastr.error('{{ trans_db('general.link_google_maps_khong_hop_le_hoac_khong_chua_toa') }}');
                        return;
                    }
                }
                $btn.addClass('opacity-50 pointer-events-none');
                var formData = new FormData(this);
                $.ajax({
                    url: '/api/reports',
                    method: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(res) {
                        toastr.success(res.message || '{{ trans_db('status.bao_cao_thanh_cong') }}');
                        setTimeout(function() {
                            window.location.href = res.redirect || '/map';
                        }, 1200);
                    },
                    error: function(xhr) {
                        if (xhr.status === 401) {
                            toastr.warning('{{ trans_db('form.ban_can_dang_nhap_de_gui_bao_cao') }}');
                            setTimeout(function() {
                                window.location.href = '/login';
                            }, 1200);
                        } else if (xhr.status === 422) {
                            let msg = "{{ trans_db('common.du_lieu_khong_hop_le') }}";
                            if (xhr.responseJSON && xhr.responseJSON.errors) {
                                msg += '\n' + Object.values(xhr.responseJSON.errors).map(e => e
                                    .join(', ')).join('\n');
                            }
                            toastr.error(msg);
                        } else {
                            toastr.error('{{ trans_db('status.da_co_loi_xay_ra_vui_long_thu_lai') }}');
                        }
                    },
                    complete: function() {
                        $btn.removeClass('opacity-50 pointer-events-none');
                    }
                });
            });
        });
    </script>
</body>

</html>

<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class DebugController extends Controller
{
    public function webhook(Request $request)
    {
        // Đường dẫn đến thư mục dự án
        $projectDir = '/home/<USER>/public_html';

        // Thay đổi thư mục và thực hiện git pull
        $command_pull = "sudo git -C $projectDir pull";
        $output = [];
        $return_var = 0;
        exec($command_pull, $output, $return_var);

        // Thiết lập thông tin bot Telegram
        $telegramToken = '**********************************************';
        $chatId = '-1002285560953';

        // Tạo thông điệp
        $message = $return_var === 0 ? "[ECO-CLEAN] Pull thành công:\n" . implode("\n", $output) : "Xảy ra lỗi khi pull:\n" . implode("\n", $output);

        // <PERSON><PERSON><PERSON> thông báo tới Telegram
        file_get_contents("https://api.telegram.org/bot$telegramToken/sendMessage?chat_id=$chatId&text=" . urlencode($message));

        // Hiển thị kết quả
        echo $message;
    }
}

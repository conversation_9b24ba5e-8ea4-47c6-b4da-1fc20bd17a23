<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Account;
use App\Models\PointHistory;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;

class ApiAuthController extends Controller
{
    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|string',
            'password' => 'required|string',
        ]);

        // Cho phép đăng nhập bằng email hoặc username
        $login_type = filter_var($request->email, FILTER_VALIDATE_EMAIL) ? 'email' : 'username';

        if (Auth::guard('account')->attempt([$login_type => $request->email, 'password' => $request->password], $request->boolean('remember'))) {
            if (!$request->header('token')) {
                $request->session()->regenerate();
            }

            $account = Auth::guard('account')->user();
            $lastPointHistory = PointHistory::where('account_id', $account->id)
                ->orderByDesc('created_at')
                ->first();
            $now = Carbon::now();
            $shouldAddPoint = false;
            if (!$lastPointHistory) {
                $shouldAddPoint = true;
            } else {
                $lastLogin = Carbon::parse($lastPointHistory->created_at);
                if ($now->diffInMinutes($lastLogin) >= 10) {
                    $shouldAddPoint = true;
                }
            }
            if ($shouldAddPoint) {
                $account->point += 5;
                $account->save();
                PointHistory::create([
                    'account_id' => $account->id,
                    'change' => 5,
                    'description' => 'Cộng điểm khi đăng nhập',
                ]);
            }
            return response()->json(['success' => true, 'message' => 'Đăng nhập thành công!']);
        }

        return response()->json([
            'success' => false,
            'message' => 'Email/tên đăng nhập hoặc mật khẩu không đúng!'
        ], 401);
    }

    public function appRegister(Request $request)
    {
        $request->validate([
            'full_name' => 'required|string',
            'email' => 'required|string',
            'password' => 'required|string',
            'confirm_password' => 'required|string',
        ]);

        $account = Account::where('email', $request->email)->first();
        if ($account) {
            return response()->json(['success' => false, 'message' => 'Email đã được đăng ký!']);
        }

        if ($request->password !== $request->confirm_password) {
            return response()->json(['success' => false, 'message' => 'Mật khẩu không khớp!']);
        }

        $email = $request->email;
        $username = explode('@', $email)[0];

        $account = Account::create([
            'full_name' => $request->full_name,
            'username' => $username,
            'email' => $request->email,
            'password' => Hash::make($request->password),
        ]);

        $token = $account->createToken('auth_token')->plainTextToken;

        return response()->json(['success' => true, 'message' => 'Đăng ký thành công!', 'token' => $token]);
    }

    public function appForgotPassword(Request $request)
    {
        $request->validate([
            'email' => 'required|string',
        ]);

        $account = Account::where('email', $request->email)->first();
        if (!$account) {
            return response()->json(['success' => false, 'message' => 'Email không tồn tại!']);
        }

        $token = Str::random(60);
        DB::table('password_resets')->insert([
            'email' => $request->email,
            'token' => $token,
            'created_at' => Carbon::now()
        ]);

        Mail::to($request->email)->send(new \App\Mail\ResetPasswordLink($token, $request->email));

        return response()->json(['success' => true, 'message' => 'Đã gửi email khôi phục mật khẩu!']);
    }

    public function logout(Request $request)
    {
        Auth::guard('account')->logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        return response()->json(['success' => true, 'message' => 'Đã đăng xuất!']);
    }
}

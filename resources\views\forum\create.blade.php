@extends('layouts.app')
@section('title', trans_db('action.tao_chu_de_moi') ?? 'Tạo chủ đề mới')
@section('header', trans_db('action.tao_chu_de_moi') ?? 'Tạo chủ đề mới')
@section('content')
<div class="container mx-auto py-8 px-4">
    <div class="max-w-2xl mx-auto bg-white rounded-2xl shadow-2xl p-8">
        <h2 class="text-2xl font-bold text-center mb-6 bg-gradient-to-r from-[#10B981] to-[#0EA5E9] bg-clip-text text-transparent">
            {{ trans_db('action.tao_chu_de_moi') ?? 'Tạo chủ đề mới' }}
        </h2>

        @if (session('success'))
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                {{ session('success') }}
            </div>
        @endif

        @if (session('error'))
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                {{ session('error') }}
            </div>
        @endif

        <form action="{{ route('forum.store', ['locale' => app()->getLocale()]) }}" method="POST">
            @csrf
            <div class="mb-6">
                <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                    {{ trans_db('general.tieu_de_chu_de') ?? 'Tiêu đề chủ đề' }} <span class="text-red-500">*</span>
                </label>
                <input type="text"
                       name="title"
                       id="title"
                       value="{{ old('title') }}"
                       class="w-full rounded-lg border border-gray-300 focus:border-[#10B981] focus:ring-[#10B981] px-4 py-3"
                       placeholder="{{ trans_db('form.nhap_tieu_de') ?? 'Nhập tiêu đề chủ đề' }}"
                       required>
                @error('title')
                    <div class="text-red-500 text-sm mt-1">{{ $message }}</div>
                @enderror
            </div>

            <div class="mb-6">
                <label for="content" class="block text-sm font-medium text-gray-700 mb-2">
                    {{ trans_db('general.noi_dung') ?? 'Nội dung' }} <span class="text-red-500">*</span>
                </label>
                <textarea name="content"
                          id="content"
                          rows="8"
                          class="w-full rounded-lg border border-gray-300 focus:border-[#10B981] focus:ring-[#10B981] px-4 py-3"
                          placeholder="{{ trans_db('form.nhap_noi_dung') ?? 'Nhập nội dung chủ đề' }}"
                          required>{{ old('content') }}</textarea>
                @error('content')
                    <div class="text-red-500 text-sm mt-1">{{ $message }}</div>
                @enderror
            </div>

            <div class="flex justify-end gap-4">
                <a href="{{ route('forum.index', ['locale' => app()->getLocale()]) }}"
                   class="px-6 py-2 bg-gray-200 text-gray-700 rounded-lg font-semibold hover:bg-gray-300 transition">
                    {{ trans_db('form.huy') ?? 'Hủy' }}
                </a>
                <button type="submit"
                        class="px-6 py-2 bg-gradient-to-r from-[#10B981] to-[#0EA5E9] text-white rounded-lg font-semibold hover:from-[#0EA5E9] hover:to-[#10B981] transition">
                    {{ trans_db('action.dang_chu_de') ?? 'Đăng chủ đề' }}
                </button>
            </div>
        </form>
    </div>
</div>
@endsection

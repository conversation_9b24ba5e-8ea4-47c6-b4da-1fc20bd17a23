<footer class="bg-[#0F172A] text-white py-16 relative overflow-hidden">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-10">
            <!-- Logo và Social Media -->
            <div class="transition-all will-change-transform duration-500 opacity-100 translate-y-0 translate-x-0 scale-100">
                <div class="space-y-6">
                    <div class="flex items-center gap-3">
                        <span class="text-xl font-bold bg-gradient-to-r from-[#00c853] to-[#4a8af4] bg-clip-text text-transparent">EcoSolves</span>
                    </div>
                    <p class="text-white/70 text-lg">{{ trans_db('general.mo_ta_ngan_ve_nen_tang') ?? 'Nền tảng giải quyết vấn đề môi trường cộng đồng' }}</p>
                    <div class="flex space-x-4">
                        <a class="text-white/60 hover:text-[#4ADE80] transition-colors p-2 bg-white/5 rounded-full" href="#">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide-facebook h-5 w-5">
                                <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
                            </svg>
                        </a>
                        <a class="text-white/60 hover:text-[#4ADE80] transition-colors p-2 bg-white/5 rounded-full" href="#">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide-twitter h-5 w-5">
                                <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path>
                            </svg>
                        </a>
                        <a class="text-white/60 hover:text-[#4ADE80] transition-colors p-2 bg-white/5 rounded-full" href="#">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide-instagram h-5 w-5">
                                <rect width="20" height="20" x="2" y="2" rx="5" ry="5"></rect>
                                <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path>
                                <line x1="17.5" x2="17.51" y1="6.5" y2="6.5"></line>
                            </svg>
                        </a>
                        <a class="text-white/60 hover:text-[#4ADE80] transition-colors p-2 bg-white/5 rounded-full" href="#">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide-linkedin h-5 w-5">
                                <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
                                <rect width="4" height="12" x="2" y="9"></rect>
                                <circle cx="4" cy="4" r="2"></circle>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Nền tảng -->
            <div class="transition-all will-change-transform duration-500 delay-150 opacity-100 translate-y-0 translate-x-0 scale-100">
                <div>
                    <h4 class="font-bold text-xl mb-6">{{ trans_db('general.nen_tang') ?? 'Nền tảng' }}</h4>
                    <ul class="space-y-4">
                        <li><a class="text-white/70 hover:text-[#4ADE80] transition-colors text-lg" href="#">{{ trans_db('nav.how_it_works') ?? 'Cách hoạt động' }}</a></li>
                        <li><a class="text-white/70 hover:text-[#4ADE80] transition-colors text-lg" href="#">{{ trans_db('nav.communities') ?? 'Cộng đồng' }}</a></li>
                        <li><a class="text-white/70 hover:text-[#4ADE80] transition-colors text-lg" href="#">{{ trans_db('nav.experts') ?? 'Chuyên gia' }}</a></li>
                        <li><a class="text-white/70 hover:text-[#4ADE80] transition-colors text-lg" href="#">{{ trans_db('status.cau_chuyen_thanh_cong') ?? 'Câu chuyện thành công' }}</a></li>
                    </ul>
                </div>
            </div>

            <!-- Tài nguyên -->
            <div class="transition-all will-change-transform duration-500 delay-300 opacity-100 translate-y-0 translate-x-0 scale-100">
                <div>
                    <h4 class="font-bold text-xl mb-6">{{ trans_db('general.tai_nguyen') ?? 'Tài nguyên' }}</h4>
                    <ul class="space-y-4">
                        <li><a class="text-white/70 hover:text-[#4ADE80] transition-colors text-lg" href="#">{{ trans_db('nav.blog') ?? 'Blog' }}</a></li>
                        <li><a class="text-white/70 hover:text-[#4ADE80] transition-colors text-lg" href="#">{{ trans_db('general.nghien_cuu') ?? 'Nghiên cứu' }}</a></li>
                        <li><a class="text-white/70 hover:text-[#4ADE80] transition-colors text-lg" href="#">{{ trans_db('general.huong_dan_moi_truong') ?? 'Hướng dẫn môi trường' }}</a></li>
                    </ul>
                </div>
            </div>

            <!-- Bản tin -->
            <div class="transition-all will-change-transform duration-500 delay-450 opacity-100 translate-y-0 translate-x-0 scale-100">
                <div>
                    <h4 class="font-bold text-xl mb-6">{{ trans_db('general.ban_tin') ?? 'Bản tin' }}</h4>
                    <p class="text-white/70 text-lg mb-6">{{ trans_db('status.nhan_thong_tin_moi_nhat_ve_moi_truong_va_cac_sang_') ?? 'Nhận thông tin mới nhất về môi trường và các sáng kiến.' }}</p>
                    <form class="space-y-4">
                        <div class="flex">
                            <input placeholder="{{ trans_db('form.nhap_email_cua_ban') ?? 'Nhập email của bạn' }}" class="px-4 py-3 bg-white/10 rounded-l-lg w-full text-white placeholder:text-white/40 focus:outline-none focus:ring-2 focus:ring-[#4ADE80]" type="email">
                            <button class="bg-gradient-to-r from-[#10B981] to-[#0EA5E9] hover:opacity-90 px-4 py-3 rounded-r-lg" type="submit">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide-arrow-right h-5 w-5">
                                    <path d="M5 12h14"></path>
                                    <path d="m12 5 7 7-7 7"></path>
                                </svg>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Footer Bottom -->
        <div class="border-t border-white/10 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center gap-4 text-center md:text-left">
            <div class="text-white/70 text-lg">
                © 2025 EcoSolves. {{ trans_db('general.da_dang_ky_ban_quyen') ?? 'Đã đăng ký bản quyền.' }}
            </div>
            <div class="flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-8">
                <a class="text-white/70 hover:text-[#4ADE80] transition-colors text-lg" href="#">{{ trans_db('general.chinh_sach_bao_mat') ?? 'Chính sách bảo mật' }}</a>
                <a class="text-white/70 hover:text-[#4ADE80] transition-colors text-lg" href="#">{{ trans_db('general.dieu_khoan_su_dung') ?? 'Điều khoản sử dụng' }}</a>
                <a class="text-white/70 hover:text-[#4ADE80] transition-colors text-lg" href="#">{{ trans_db('general.chinh_sach_cookie') ?? 'Chính sách Cookie' }}</a>
            </div>
        </div>
    </div>
</footer>

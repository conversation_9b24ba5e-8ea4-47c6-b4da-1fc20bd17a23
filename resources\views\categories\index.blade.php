@extends('layouts.app')

@section('title', trans_db('general.danh_muc') ?? 'Danh mục' )

@section('header', trans_db('general.tat_ca_danh_muc') ?? 'Tất cả danh mục')

@section('content')
    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        @forelse($categories ?? [] as $category)
            <div class="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow overflow-hidden border">
                @if($category->image)
                    <img src="{{ $category->image }}" alt="{{ $category->name }}" class="w-full h-40 object-cover">
                @else
                    <div class="w-full h-40 bg-gray-200 flex items-center justify-center">
                        <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                @endif
                <div class="p-4">
                    <h3 class="text-lg font-semibold mb-2">
                        <a href="{{ route('categories.show', $category) }}" class="text-indigo-600 hover:text-indigo-800">{{ $category->name }}</a>
                    </h3>
                    @if($category->description)
                        <p class="text-gray-600 mb-3 text-sm">{{ Str::limit($category->description, 100) }}</p>
                    @endif
                    <div class="flex justify-between items-center text-sm">
                        <span class="text-gray-500">{{ $category->posts_count ?? 0 }} {{ trans_db('general.bai_viet') }}</span>
                        <a href="{{ route('categories.show', $category) }}" class="text-indigo-600 hover:text-indigo-800 font-medium">{{ trans_db('action.xem_chi_tiet_icon') }}</a>
                    </div>
                </div>
            </div>
        @empty
            <div class="col-span-full bg-gray-50 rounded-lg p-6 text-center">
                <p class="text-gray-500">{{ trans_db('common.chua_co_danh_muc_nao') }}</p>
            </div>
        @endforelse
    </div>

    @if(isset($categories) && $categories->hasPages())
        <div class="mt-6">
            {{ $categories->links() }}
        </div>
    @endif
@endsection

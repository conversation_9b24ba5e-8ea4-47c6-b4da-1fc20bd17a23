<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Event>
 */
class EventFactory extends Factory
{
    public function definition(): array
    {
        $title = $this->faker->sentence(4);
        $start = $this->faker->dateTimeBetween('+1 days', '+1 month');
        $end = (clone $start)->modify('+'.rand(1,3).' hours');
        return [
            'title' => $title,
            'slug' => Str::slug($title) . '-' . Str::random(5),
            'description' => $this->faker->sentence(15),
            'content' => $this->faker->paragraph(5),
            'location' => $this->faker->address(),
            'start_date' => $start,
            'end_date' => $end,
            'featured_image' => null,
            'is_active' => true,
            'max_participants' => $this->faker->numberBetween(20, 200),
            'created_by' => \App\Models\Account::inRandomOrder()->value('id'),
        ];
    }
} 
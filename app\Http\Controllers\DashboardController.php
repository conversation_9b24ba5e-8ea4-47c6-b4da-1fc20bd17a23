<?php

namespace App\Http\Controllers;

use App\Models\Account;
use App\Models\Report;
use App\Models\Gift;
use App\Models\PointHistory;
use App\Models\GiftRedemption;
use App\Models\Event;
use App\Models\EventRegistration;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class DashboardController extends Controller
{
    public function index()
    {
        try {
            // Lấy thông tin tài khoản đang đăng nhập
            $account = Auth::guard('account')->user();
            
            if (!$account) {
                return redirect()->route('login', ['locale' => app()->getLocale()]);
            }
            
            // Thông tin cá nhân
            $personalInfo = [
                'username' => $account->username ?? '',
                'email' => $account->email ?? '',
                'full_name' => $account->full_name ?? '',
                'phone_number' => $account->phone_number ?? '',
                'address' => $account->address ?? '',
                'avatar' => $account->avatar ?? '',
                'point' => $account->point ?? 0,
                'is_active' => $account->is_active ?? false,
                'created_at' => $account->created_at ?? now(),
            ];
            
            // Lịch sử tích điểm (10 bản ghi gần nhất)
            $pointHistory = PointHistory::where('account_id', $account->id)
                ->orderBy('created_at', 'desc')
                ->take(10)
                ->get();
            
            // Lịch sử báo cáo (10 bản ghi gần nhất)
            $reportHistory = Report::where('created_by', $account->id)
                ->orderBy('created_at', 'desc')
                ->take(10)
                ->get();
            
            // Lịch sử đổi quà (10 bản ghi gần nhất)
            $redemptionHistory = GiftRedemption::with('gift')
                ->where('account_id', $account->id)
                ->orderBy('created_at', 'desc')
                ->take(10)
                ->get();
            
            // Thống kê cá nhân
            $personalStats = [
                'total_reports' => Report::where('created_by', $account->id)->count(),
                'approved_reports' => Report::where('created_by', $account->id)->where('status', 'approved')->count(),
                'pending_reports' => Report::where('created_by', $account->id)->where('status', 'pending')->count(),
                'total_redemptions' => GiftRedemption::where('account_id', $account->id)->count(),
                'total_points_earned' => PointHistory::where('account_id', $account->id)->where('change', '>', 0)->sum('change'),
                'total_points_spent' => abs(PointHistory::where('account_id', $account->id)->where('change', '<', 0)->sum('change')),
            ];
            
            // Danh sách quà tặng có thể đổi (sử dụng bảng gifts)
            $availableGifts = Gift::where('is_active', true)
                ->where('quantity', '>', 0)
                ->where('available_from', '<=', now())
                ->where('available_until', '>=', now())
                ->where('value', '<=', $account->point)
                ->orderBy('value', 'asc')
                ->get();
            
            return view('dashboard', compact(
                'personalInfo',
                'pointHistory',
                'reportHistory',
                'redemptionHistory',
                'personalStats',
                'availableGifts'
            ));
        } catch (\Exception $e) {
            // Log lỗi
            \Log::error('Dashboard error: ' . $e->getMessage());
            
            // Trả về view với dữ liệu mặc định
            $personalInfo = [
                'username' => '',
                'email' => '',
                'full_name' => '',
                'phone_number' => '',
                'address' => '',
                'avatar' => '',
                'point' => 0,
                'is_active' => false,
                'created_at' => now(),
            ];
            
            $pointHistory = collect();
            $reportHistory = collect();
            $redemptionHistory = collect();
            $personalStats = [
                'total_reports' => 0,
                'approved_reports' => 0,
                'pending_reports' => 0,
                'total_redemptions' => 0,
                'total_points_earned' => 0,
                'total_points_spent' => 0,
            ];
            $availableGifts = collect();
            
            return view('dashboard', compact(
                'personalInfo',
                'pointHistory',
                'reportHistory',
                'redemptionHistory',
                'personalStats',
                'availableGifts'
            ));
        }
    }

    public function edit()
    {
        $account = Auth::guard('account')->user();
        
        if (!$account) {
            return redirect()->route('login', ['locale' => app()->getLocale()]);
        }
        
        return view('dashboard.edit', compact('account'));
    }

    public function update(Request $request)
    {
        $account = Auth::guard('account')->user();
        
        if (!$account) {
            return redirect()->route('login', ['locale' => app()->getLocale()]);
        }
        
        $request->validate([
            'full_name' => 'required|string|max:255',
            'phone_number' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);
        
        try {
            $account->full_name = $request->full_name;
            $account->phone_number = $request->phone_number;
            $account->address = $request->address;
            
            // Xử lý upload avatar
            if ($request->hasFile('avatar')) {
                // Xóa avatar cũ nếu có
                if ($account->avatar && file_exists(storage_path('app/public/' . $account->avatar))) {
                    unlink(storage_path('app/public/' . $account->avatar));
                }
                
                $avatarPath = $request->file('avatar')->store('avatars', 'public');
                $account->avatar = $avatarPath;
            }
            
            $account->save();
            
            return redirect()->route('dashboard', ['locale' => app()->getLocale()])->with('success', 'Cập nhật thông tin thành công!');
        } catch (\Exception $e) {
            \Log::error('Update profile error: ' . $e->getMessage());
            return back()->with('error', 'Có lỗi xảy ra khi cập nhật thông tin!');
        }
    }

    public function statistics()
    {
        $account = Auth::guard('account')->user();
        
        if (!$account) {
            return redirect()->route('login', ['locale' => app()->getLocale()]);
        }
        
        // Thống kê cá nhân
        $personalStats = [
            'total_reports' => Report::where('created_by', $account->id)->count(),
            'approved_reports' => Report::where('created_by', $account->id)->where('status', 'approved')->count(),
            'pending_reports' => Report::where('created_by', $account->id)->where('status', 'pending')->count(),
            'rejected_reports' => Report::where('created_by', $account->id)->where('status', 'rejected')->count(),
            'total_redemptions' => GiftRedemption::where('account_id', $account->id)->count(),
            'approved_redemptions' => GiftRedemption::where('account_id', $account->id)->where('status', 'approved')->count(),
            'completed_redemptions' => GiftRedemption::where('account_id', $account->id)->where('status', 'completed')->count(),
            'total_points_earned' => PointHistory::where('account_id', $account->id)->where('change', '>', 0)->sum('change'),
            'total_points_spent' => abs(PointHistory::where('account_id', $account->id)->where('change', '<', 0)->sum('change')),
        ];
        
        // Lịch sử tích điểm (20 bản ghi gần nhất)
        $pointHistory = PointHistory::where('account_id', $account->id)
            ->orderBy('created_at', 'desc')
            ->take(20)
            ->get();
        
        // Lịch sử báo cáo (20 bản ghi gần nhất)
        $reportHistory = Report::where('created_by', $account->id)
            ->orderBy('created_at', 'desc')
            ->take(20)
            ->get();
        
        // Lịch sử đổi quà (20 bản ghi gần nhất)
        $redemptionHistory = GiftRedemption::with('gift')
            ->where('account_id', $account->id)
            ->orderBy('created_at', 'desc')
            ->take(20)
            ->get();
        
        return view('dashboard.statistics', compact('personalStats', 'pointHistory', 'reportHistory', 'redemptionHistory'));
    }

    public function profile()
    {
        $account = Auth::guard('account')->user();
        
        if (!$account) {
            return redirect()->route('login', ['locale' => app()->getLocale()]);
        }
        
        // Thông tin cá nhân
        $personalInfo = [
            'username' => $account->username ?? '',
            'email' => $account->email ?? '',
            'full_name' => $account->full_name ?? '',
            'phone_number' => $account->phone_number ?? '',
            'address' => $account->address ?? '',
            'avatar' => $account->avatar ?? '',
            'point' => $account->point ?? 0,
            'is_active' => $account->is_active ?? false,
            'created_at' => $account->created_at ?? now(),
        ];
        
        return view('dashboard.profile', compact('personalInfo'));
    }

    public function gifts()
    {
        $account = Auth::guard('account')->user();
        
        if (!$account) {
            return redirect()->route('login', ['locale' => app()->getLocale()]);
        }
        
        // Danh sách quà tặng có thể đổi
        $availableGifts = Gift::where('is_active', true)
            ->where('quantity', '>', 0)
            ->where('available_from', '<=', now())
            ->where('available_until', '>=', now())
            ->where('value', '<=', $account->point)
            ->orderBy('value', 'asc')
            ->get();
        
        // Lịch sử đổi quà (10 bản ghi gần nhất)
        $redemptionHistory = GiftRedemption::with('gift')
            ->where('account_id', $account->id)
            ->orderBy('created_at', 'desc')
            ->take(10)
            ->get();
        
        return view('dashboard.gifts', compact('availableGifts', 'redemptionHistory'));
    }

    // Hiển thị form tạo sự kiện
    public function createEventForm()
    {
        return view('dashboard.create_event');
    }

    // Xử lý lưu sự kiện mới
    public function storeEvent(Request $request)
    {
        $account = Auth::guard('account')->user();
        if (!$account) {
            return redirect()->route('login', ['locale' => app()->getLocale()]);
        }
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'content' => 'nullable|string',
            'location' => 'nullable|string|max:255',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:4096',
            'max_participants' => 'nullable|integer|min:1',
        ]);
        try {
            $data = $request->only(['title', 'description', 'content', 'location', 'start_date', 'end_date', 'max_participants']);
            $data['slug'] = Str::slug($request->title) . '-' . uniqid();
            $data['is_active'] = false;
            $data['status'] = 'pending';
            $data['created_by'] = $account->id;
            // Xử lý upload ảnh
            if ($request->hasFile('featured_image')) {
                $imagePath = $request->file('featured_image')->store('events/images', 'public');
                $data['featured_image'] = $imagePath;
            }
            Event::create($data);
            return redirect()->route('dashboard', ['locale' => app()->getLocale()])->with('success', 'Tạo sự kiện thành công! Sự kiện sẽ được admin phê duyệt trước khi hiển thị.');
        } catch (\Exception $e) {
            \Log::error('Create event error: ' . $e->getMessage());
            return back()->with('error', 'Có lỗi xảy ra khi tạo sự kiện!');
        }
    }

    public function myEvents()
    {
        $account = Auth::guard('account')->user();
        if (!$account) {
            return redirect()->route('login', ['locale' => app()->getLocale()]);
        }
        // Sự kiện do mình tạo
        $createdEvents = $account->createdEvents()->orderByDesc('created_at')->get();

        // Sự kiện mình đã đăng ký tham gia (trừ các sự kiện do mình tạo)
        $joinedEventIds = $account->eventRegistrations()->pluck('event_id')->toArray();

        $joinedEvents = Event::whereIn('id', $joinedEventIds)
            ->where('created_by', '!=', $account->id)
            ->orderByDesc('start_date')
            ->get();


        return view('dashboard.events', compact('createdEvents', 'joinedEvents'));
    }
}

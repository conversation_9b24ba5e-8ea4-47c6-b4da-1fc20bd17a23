<x-guest-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ trans_db('general.trang_ca_nhan') ?? 'Trang cá nhân' }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="flex min-h-[400px]">
                    <!-- Sidebar -->
                    @include('dashboard.sidebar')

                    <!-- Main Content -->
                    <div class="w-3/4 p-6">
                        @if (session('success'))
                            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                                {{ session('success') }}
                            </div>
                        @endif

                        @if (session('error'))
                            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                                {{ session('error') }}
                            </div>
                        @endif

                        <!-- Thông tin cá nhân -->
                        <div class="mb-8">
                            <div class="flex justify-between items-center mb-6">
                                <h3 class="text-xl font-bold text-gray-800">{{ trans_db('status.thong_tin_ca_nhan') ?? 'Thông tin cá nhân' }}</h3>
                                <a href="{{ route('dashboard.edit', ['locale' => app()->getLocale()]) }}" class="text-gray-800 font-semibold hover:text-[#10B981] transition">{{ trans_db('action.chinh_sua_thong_tin') ?? 'Chỉnh sửa thông tin' }}</a>
                            </div>
                            <div class="bg-gray-50 rounded-lg p-6">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <!-- Avatar và thông tin cơ bản -->
                                    <div class="flex items-center gap-4">
                                        <img src="{{ $personalInfo['avatar'] ? asset('storage/' . $personalInfo['avatar']) : 'https://ui-avatars.com/api/?name=' . urlencode($personalInfo['full_name']) }}"
                                            class="w-24 h-24 rounded-full object-cover border-4 border-white shadow-lg"
                                            alt="Avatar">
                                        <div>
                                            <h4 class="font-bold text-xl text-gray-800">{{ $personalInfo['full_name'] }}</h4>
                                            <p class="text-gray-600">{{ $personalInfo['username'] }}</p>
                                            <p class="text-gray-600">{{ $personalInfo['email'] }}</p>
                                        </div>
                                    </div>

                                    <!-- Thông tin chi tiết -->
                                    <div class="space-y-3">
                                        <div class="flex justify-between items-center p-3 bg-white rounded border">
                                            <span class="font-semibold text-gray-700">{{ trans_db('form.so_dien_thoai') ?? 'Số điện thoại' }}</span>
                                            <span class="text-gray-600">{{ $personalInfo['phone_number'] ?? trans_db('action.chua_cap_nhat') ?? 'Chưa cập nhật' }}</span>
                                        </div>
                                        <div class="flex justify-between items-center p-3 bg-white rounded border">
                                            <span class="font-semibold text-gray-700">{{ trans_db('form.dia_chi') ?? 'Địa chỉ' }}</span>
                                            <span class="text-gray-600">{{ $personalInfo['address'] ?? trans_db('action.chua_cap_nhat') ?? 'Chưa cập nhật' }}</span>
                                        </div>
                                        <div class="flex justify-between items-center p-3 bg-white rounded border">
                                            <span class="font-semibold text-gray-700">{{ trans_db('general.diem_tich_luy') ?? 'Điểm tích lũy' }}</span>
                                            <span class="text-green-600 font-bold text-lg">{{ number_format($personalInfo['point']) }} {{ trans_db('general.diem') ?? 'điểm' }}</span>
                                        </div>
                                        <div class="flex justify-between items-center p-3 bg-white rounded border">
                                            <span class="font-semibold text-gray-700">{{ trans_db('time.ngay_tham_gia') ?? 'Ngày tham gia' }}</span>
                                            <span class="text-gray-600">{{ $personalInfo['created_at']->format('d/m/Y') }}</span>
                                        </div>
                                        <div class="flex justify-between items-center p-3 bg-white rounded border">
                                            <span class="font-semibold text-gray-700">{{ trans_db('general.trang_thai') ?? 'Trạng thái' }}</span>
                                            <span class="px-2 py-1 rounded text-xs font-medium {{ $personalInfo['is_active'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                                {{ $personalInfo['is_active'] ? trans_db('general.hoat_dong') ?? 'Hoạt động' : trans_db('common.khong_hoat_dong') ?? 'Không hoạt động' }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Thống kê nhanh -->
                        <div class="mb-8">
                            <h3 class="text-xl font-bold mb-4 text-gray-800">{{ trans_db('general.thong_ke_nhanh') ?? 'Thống kê nhanh' }}</h3>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                <div class="bg-blue-50 p-4 rounded-lg border text-center">
                                    <div class="text-2xl font-bold text-blue-600">{{ number_format($personalInfo['point']) }}</div>
                                    <div class="text-sm text-gray-600">{{ trans_db('general.diem_hien_tai') ?? 'Điểm hiện tại' }}</div>
                                </div>
                                <div class="bg-green-50 p-4 rounded-lg border text-center">
                                    <div class="text-2xl font-bold text-green-600">{{ $personalInfo['created_at']->format('d') }}</div>
                                    <div class="text-sm text-gray-600">{{ trans_db('time.ngay_tham_gia') ?? 'Ngày tham gia' }}</div>
                                </div>
                                <div class="bg-orange-50 p-4 rounded-lg border text-center">
                                    <div class="text-2xl font-bold text-orange-600">{{ $personalInfo['created_at']->format('m') }}</div>
                                    <div class="text-sm text-gray-600">{{ trans_db('time.thang_tham_gia') ?? 'Tháng tham gia' }}</div>
                                </div>
                                <div class="bg-purple-50 p-4 rounded-lg border text-center">
                                    <div class="text-2xl font-bold text-purple-600">{{ $personalInfo['created_at']->format('Y') }}</div>
                                    <div class="text-sm text-gray-600">{{ trans_db('time.nam_tham_gia') ?? 'Năm tham gia' }}</div>
                                </div>
                            </div>
                        </div>

                        <!-- Hướng dẫn sử dụng -->
                        <div class="mb-8">
                            <h3 class="text-xl font-bold mb-4 text-gray-800">{{ trans_db('general.huong_dan_su_dung') ?? 'Hướng dẫn sử dụng' }}</h3>
                            <div class="bg-blue-50 rounded-lg p-6">
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div class="text-center">
                                        <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                            <span class="text-blue-600 font-bold">1</span>
                                        </div>
                                        <h4 class="font-semibold text-gray-800 mb-2">{{ trans_db('general.bao_cao_van_de') ?? 'Báo cáo vấn đề' }}</h4>
                                        <p class="text-sm text-gray-600">{{ trans_db('common.chup_anh_va_bao_cao_cac_van_de_moi_truong_de_tich_diem') ?? 'Chụp ảnh và báo cáo các vấn đề môi trường để tích điểm' }}</p>
                                    </div>
                                    <div class="text-center">
                                        <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                            <span class="text-green-600 font-bold">2</span>
                                        </div>
                                        <h4 class="font-semibold text-gray-800 mb-2">{{ trans_db('general.tich_diem') ?? 'Tích điểm' }}</h4>
                                        <p class="text-sm text-gray-600">{{ trans_db('general.nhan_diem_thuong_khi_bao_cao_duoc_duyet') ?? 'Nhận điểm thưởng khi báo cáo được duyệt' }}</p>
                                    </div>
                                    <div class="text-center">
                                        <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                            <span class="text-purple-600 font-bold">3</span>
                                        </div>
                                        <h4 class="font-semibold text-gray-800 mb-2">{{ trans_db('general.doi_qua') ?? 'Đổi quà' }}</h4>
                                        <p class="text-sm text-gray-600">{{ trans_db('common.su_dung_diem_de_doi_qua_hap_dan') ?? 'Sử dụng điểm để đổi lấy các phần quà hấp dẫn' }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-guest-layout>

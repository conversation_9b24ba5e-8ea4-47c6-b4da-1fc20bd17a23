<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Gift;

class GiftSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $gifts = [
            [
                'name' => 'Voucher Shopee 50K',
                'description' => 'Voucher giảm giá Shopee trị giá 50.000 VNĐ, áp dụng cho đơn hàng từ 100.000 VNĐ',
                'image' => null,
                'value' => 50,
                'quantity' => 50,
                'total_quantity' => 50,
                'is_active' => true,
                'available_from' => now(),
                'available_until' => now()->addMonths(6),
            ],
            [
                'name' => 'Voucher Grab 30K',
                'description' => 'Voucher giảm giá Grab trị giá 30.000 VNĐ, áp dụng cho dịch vụ giao hàng và đi xe',
                'image' => null,
                'value' => 30,
                'quantity' => 30,
                'total_quantity' => 30,
                'is_active' => true,
                'available_from' => now(),
                'available_until' => now()->addMonths(6),
            ],
            [
                'name' => 'Thẻ cào điện thoại 20K',
                'description' => 'Thẻ cào điện thoại trị giá 20.000 VNĐ, áp dụng cho tất cả nhà mạng',
                'image' => null,
                'value' => 20,
                'quantity' => 100,
                'total_quantity' => 100,
                'is_active' => true,
                'available_from' => now(),
                'available_until' => now()->addMonths(6),
            ],
            [
                'name' => 'Voucher Tiki 100K',
                'description' => 'Voucher giảm giá Tiki trị giá 100.000 VNĐ, áp dụng cho đơn hàng từ 200.000 VNĐ',
                'image' => null,
                'value' => 100,
                'quantity' => 20,
                'total_quantity' => 20,
                'is_active' => true,
                'available_from' => now(),
                'available_until' => now()->addMonths(6),
            ],
            [
                'name' => 'Ly sứ cao cấp',
                'description' => 'Ly sứ cao cấp với thiết kế đẹp, dung tích 350ml, phù hợp cho cà phê và trà',
                'image' => null,
                'value' => 150,
                'quantity' => 25,
                'total_quantity' => 25,
                'is_active' => true,
                'available_from' => now(),
                'available_until' => now()->addMonths(6),
            ],
            [
                'name' => 'Bình nước giữ nhiệt',
                'description' => 'Bình nước giữ nhiệt 500ml, giữ nóng 12 giờ, giữ lạnh 24 giờ',
                'image' => null,
                'value' => 300,
                'quantity' => 15,
                'total_quantity' => 15,
                'is_active' => true,
                'available_from' => now(),
                'available_until' => now()->addMonths(6),
            ],
            [
                'name' => 'Túi vải canvas',
                'description' => 'Túi vải canvas thân thiện môi trường, kích thước 40x35cm, có in logo ứng dụng',
                'image' => null,
                'value' => 120,
                'quantity' => 40,
                'total_quantity' => 40,
                'is_active' => true,
                'available_from' => now(),
                'available_until' => now()->addMonths(6),
            ],
            [
                'name' => 'Mũ bảo hiểm',
                'description' => 'Mũ bảo hiểm xe máy chất lượng cao, đạt tiêu chuẩn an toàn, có nhiều màu sắc',
                'image' => null,
                'value' => 400,
                'quantity' => 10,
                'total_quantity' => 10,
                'is_active' => true,
                'available_from' => now(),
                'available_until' => now()->addMonths(6),
            ],
            [
                'name' => 'Voucher CGV 2D',
                'description' => 'Voucher xem phim CGV 2D trị giá 80.000 VNĐ, áp dụng cho tất cả rạp CGV',
                'image' => null,
                'value' => 80,
                'quantity' => 35,
                'total_quantity' => 35,
                'is_active' => true,
                'available_from' => now(),
                'available_until' => now()->addMonths(6),
            ],
            [
                'name' => 'Sổ tay bảo vệ môi trường',
                'description' => 'Sổ tay với thiết kế đẹp, giấy tái chế, có in các tips bảo vệ môi trường',
                'image' => null,
                'value' => 90,
                'quantity' => 60,
                'total_quantity' => 60,
                'is_active' => true,
                'available_from' => now(),
                'available_until' => now()->addMonths(6),
            ],
            [
                'name' => 'Voucher Starbucks 50K',
                'description' => 'Voucher Starbucks trị giá 50.000 VNĐ, áp dụng cho đồ uống và bánh ngọt',
                'image' => null,
                'value' => 50,
                'quantity' => 25,
                'total_quantity' => 25,
                'is_active' => true,
                'available_from' => now(),
                'available_until' => now()->addMonths(6),
            ],
            [
                'name' => 'Bộ bút viết cao cấp',
                'description' => 'Bộ bút viết 3 cây với thiết kế đẹp, mực chất lượng cao, phù hợp cho học tập và công việc',
                'image' => null,
                'value' => 180,
                'quantity' => 30,
                'total_quantity' => 30,
                'is_active' => true,
                'available_from' => now(),
                'available_until' => now()->addMonths(6),
            ],
            [
                'name' => 'Voucher Pizza Hut 100K',
                'description' => 'Voucher Pizza Hut trị giá 100.000 VNĐ, áp dụng cho pizza và đồ uống',
                'image' => null,
                'value' => 100,
                'quantity' => 15,
                'total_quantity' => 15,
                'is_active' => true,
                'available_from' => now(),
                'available_until' => now()->addMonths(6),
            ],
            [
                'name' => 'Áo thun cotton',
                'description' => 'Áo thun cotton 100%, có in logo ứng dụng, nhiều size và màu sắc',
                'image' => null,
                'value' => 220,
                'quantity' => 20,
                'total_quantity' => 20,
                'is_active' => true,
                'available_from' => now(),
                'available_until' => now()->addMonths(6),
            ],
            [
                'name' => 'Voucher Vincom 200K',
                'description' => 'Voucher Vincom trị giá 200.000 VNĐ, áp dụng cho mua sắm tại các trung tâm Vincom',
                'image' => null,
                'value' => 200,
                'quantity' => 8,
                'total_quantity' => 8,
                'is_active' => true,
                'available_from' => now(),
                'available_until' => now()->addMonths(6),
            ],
        ];

        foreach ($gifts as $gift) {
            Gift::create($gift);
        }
    }
} 
<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Session;
use App\Models\Account;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;

class ApiRegisterController extends Controller
{
    // Bước 1: Gửi mã xác thực về email
    public function sendVerificationCode(Request $request)
    {
        $request->validate([
            'email' => 'required|email|unique:accounts,email',
        ]);
        $email = $request->email;
        $code = rand(100000, 999999);
        $expire = now()->addMinutes(10);
        // Lưu vào cache
        $cacheKey = 'register_' . md5($email);
        Cache::put($cacheKey, [
            'email' => $email,
            'code' => $code,
            'expire' => $expire->toDateTimeString(),
            'verified' => false,
        ], 600); // 10 phút
        // Gửi email (dùng mail mặc định, bạn có thể custom lại)
        try {
            Mail::to($email)->send(new \App\Mail\RegisterVerificationCode($code, $email));
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Không gửi được email. Vui lòng thử lại.'], 500);
        }
        return response()->json(['success' => true]);
    }

    // Bước 2: Kiểm tra mã xác thực
    public function checkVerificationCode(Request $request)
    {
        $request->validate([
            'code' => 'required',
            'email' => 'required|email',
        ]);
        $email = $request->email;
        $code = $request->code;
        $cacheKey = 'register_' . md5($email);
        $data = Cache::get($cacheKey);
        if (!$data) {
            return response()->json(['success' => false, 'message' => 'Mã xác thực đã hết hạn hoặc không tồn tại.'], 400);
        }
        $expire = Carbon::parse($data['expire']);
        if (now()->gt($expire)) {
            Cache::forget($cacheKey);
            return response()->json(['success' => false, 'message' => 'Mã xác thực đã hết hạn. Vui lòng lấy lại mã mới.'], 400);
        }
        if ($code != $data['code']) {
            return response()->json(['success' => false, 'message' => 'Mã xác thực không đúng.'], 400);
        }
        // Đánh dấu đã xác thực thành công
        $data['verified'] = true;
        $ttl = now()->diffInSeconds($expire);
        if ($ttl <= 0) {
            Cache::forget($cacheKey);
            return response()->json(['success' => false, 'message' => 'Mã xác thực đã hết hạn. Vui lòng lấy lại mã mới.'], 400);
        }
        $data['verified'] = true;
        Cache::put($cacheKey, $data, $ttl);
        return response()->json(['success' => true]);
    }

    // Bước 3: Nhập username, full_name, password, tạo tài khoản
    public function completeRegistration(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'username' => 'required|string|max:50|unique:accounts,username',
            'full_name' => 'required|string|max:255',
            'password' => 'required|string|min:6|confirmed',
        ]);
        $email = $request->email;
        $cacheKey = 'register_' . md5($email);
        $data = Cache::get($cacheKey);
        if (!$data || empty($data['verified'])) {
            return response()->json(['success' => false, 'message' => 'Bạn chưa xác thực email hoặc mã đã hết hạn.'], 400);
        }
        // Tạo tài khoản
        $account = Account::create([
            'username' => $request->username,
            'full_name' => $request->full_name,
            'email' => $email,
            'password' => Hash::make($request->password),
            'email_verified_at' => now()
        ]);
        Auth::login($account);
        // Xóa cache đăng ký
        Cache::forget($cacheKey);
        return response()->json(['success' => true, 'redirect' => route('dashboard')]);
    }
}

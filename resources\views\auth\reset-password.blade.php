<x-guest-layout>
    <div class="w-full min-h-screen flex flex-col justify-center items-center bg-gradient-to-br from-[#0EA5E9]/10 via-[#10B981]/10 to-[#4ADE80]/10 py-12 px-4">
        <div class="w-full max-w-md bg-white rounded-2xl shadow-2xl p-8 md:p-10">
            <h2 class="text-3xl font-bold text-center mb-8 bg-gradient-to-r from-[#10B981] to-[#0EA5E9] bg-clip-text text-transparent">{{ trans_db('form.dat_lai_mat_khau') }}</h2>
            <form method="POST" action="{{ route('reset-password.post') }}" class="space-y-6">
                @csrf
                <!-- Password Reset Token -->
                <input type="hidden" value="{{ $request->route('token') }}">
                <input type="hidden" name="token" value="{{ $token }}">
                <!-- Email Address -->
                <div>
                    <x-input-label for="email" :value=""{{ trans_db('form.email') }}"" class="text-[#10B981] font-semibold mt-1 w-full rounded-lg border border-[#10B981]/30 focus:border-[#10B981] focus:ring-[#10B981]" type="email('email', $request->email)" required autofocus autocomplete="username('form.nhap_email') }}" />
                    <x-input-label for="email" :value="'Email'" class="text-[#10B981] font-semibold" />
                    <x-text-input id="email" class="block mt-1 w-full rounded-lg border border-[#10B981]/30 focus:border-[#10B981] focus:ring-[#10B981]" type="email" name="email" :value="old('email', $email)" required autofocus autocomplete="username" placeholder="Nhập email" />
                    <x-input-error :messages="$errors->get('email')" class="mt-2" />
                </div>
                <!-- Password -->
                <div>
                    <x-input-label for="password" :value=""{{ trans_db('form.mat_khau_moi') }}"" class="text-[#10B981] font-semibold mt-1 w-full rounded-lg border border-[#10B981]/30 focus:border-[#10B981] focus:ring-[#10B981]">{{ trans_db('form.nhap_mat_khau_moi') }}
                    <x-input-error :messages="$errors->get('password')" class="mt-2" />
                </div>
                <!-- Confirm Password -->
                <div>
                    <x-input-label for="password_confirmation" :value=""{{ trans_db('form.xac_nhan_mat_khau_moi') }}"" class="text-[#10B981] font-semibold mt-1 w-full rounded-lg border border-[#10B981]/30 focus:border-[#10B981] focus:ring-[#10B981]">{{ trans_db('form.nhap_lai_mat_khau_moi') }}
                    <x-input-error :messages="$errors->get('password_confirmation')" class="mt-2" />
                </div>
                <div class="flex items-center justify-end mt-4">
                    <button type="submit">{{ trans_db('form.dat_lai_mat_khau') }}
                </div>
            </form>
        </div>
    </div>
</x-guest-layout>

<?php $__env->startSection('title', trans_db('nav.trang_chu')); ?>

<?php $__env->startSection('header', trans_db('general.chao_mung_den_voi_ecosolves')); ?>

<?php $__env->startSection('content'); ?>
    <section
        class="relative overflow-hidden bg-gradient-to-br from-[#0EA5E9]/10 via-[#10B981]/15 to-[#4ADE80]/10 py-24 md:py-36">
        <div class="container mx-auto">
            <div class="absolute inset-0 z-0 overflow-hidden">
                <div
                    class="absolute -top-[30%] -right-[10%] h-[600px] w-[600px] rounded-full bg-gradient-to-br from-[#4ADE80]/20 to-[#0EA5E9]/20 blur-3xl">
                </div>
                <div
                    class="absolute -bottom-[30%] -left-[10%] h-[600px] w-[600px] rounded-full bg-gradient-to-tr from-[#10B981]/20 to-[#0EA5E9]/20 blur-3xl">
                </div>
                <svg class="absolute right-0 top-0 h-full w-1/2 translate-x-1/4 transform text-[#4ADE80]/10"
                    viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                    <path fill="currentColor"
                        d="M37.5,-65.1C48.9,-55.3,58.9,-44.9,67.1,-32.2C75.3,-19.5,81.7,-4.6,79.8,9.2C77.9,23,67.8,35.6,56.4,44.9C45,54.2,32.4,60.2,18.8,65.2C5.2,70.2,-9.3,74.3,-22.6,71.7C-35.9,69.1,-47.9,59.9,-58.3,48.2C-68.7,36.5,-77.5,22.4,-79.2,7.3C-80.9,-7.8,-75.5,-23.8,-66.6,-36.9C-57.7,-50,-45.3,-60.2,-32,-67.4C-18.7,-74.6,-4.7,-78.9,7.9,-75.5C20.5,-72.1,41,-74.9,37.5,-65.1Z"
                        transform="translate(100 100)"></path>
                </svg>
                <svg class="absolute left-0 bottom-0 h-full w-1/2 -translate-x-1/4 transform text-[#10B981]/10"
                    viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                    <path fill="currentColor"
                        d="M45.3,-78.2C58.3,-71.3,68.4,-58.1,76.3,-43.9C84.2,-29.7,89.8,-14.8,88.7,-0.6C87.6,13.6,79.8,27.2,71.7,41.1C63.6,55,55.2,69.2,42.6,77.8C30,86.3,13.2,89.2,-2.4,87.5C-18,85.8,-32.4,79.5,-45.3,70.7C-58.2,61.9,-69.5,50.6,-76.7,36.8C-83.9,23,-87,6.7,-85.9,-9.2C-84.8,-25.1,-79.5,-40.7,-69.7,-53.5C-59.9,-66.3,-45.6,-76.3,-31.1,-82.1C-16.6,-87.9,-1.9,-89.5,11.9,-86.5C25.7,-83.5,51.4,-76,45.3,-78.2Z"
                        transform="translate(100 100)"></path>
                </svg>
            </div>
            <div class="relative z-10 grid gap-8 lg:grid-cols-2 lg:gap-16">
                <div
                    class="flex flex-col justify-center space-y-6">
                    <div
                        class="inline-flex items-center rounded-full border text-xs font-semibold w-fit bg-white/20 text-[#10B981] backdrop-blur-sm border-[#10B981]/20 px-4 py-1"><?php echo e(trans_db('general.nen_tang_hanh_dong_vi_moi_truong')); ?></div>
                    <h1 class="text-4xl font-bold tracking-tighter sm:text-5xl md:text-6xl lg:text-7xl"><?php echo e(trans_db('general.giai_phap_dia_phuong_cho')); ?><span
                            class="bg-gradient-to-r from-[#10B981] to-[#0EA5E9] bg-clip-text text-transparent"><?php echo e(trans_db('general.tuong_lai_ben_vung')); ?></span></h1>
                    <p class="max-w-[600px] text-foreground text-lg md:text-xl"><?php echo e(trans_db('action.tham_gia_cong_dong_nhung_nguoi_tao_ra_thay_doi_cun')); ?></p>
                    <div class="flex flex-col sm:flex-row gap-4 pt-4">
                        <a
                            class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md font-medium bg-gradient-to-r from-[#10B981] to-[#0EA5E9] hover:opacity-90 text-white shadow-md px-6 py-6 h-auto text-lg"
                            href="<?php echo e(localized_route('pages.report')); ?>"><?php echo e(trans_db('general.bao_cao_van_de')); ?></a>
                        <a
                            class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md font-medium border bg-background border-[#10B981] text-[#10B981] hover:bg-[#10B981]/10 shadow-sm px-6 py-6 h-auto text-lg"
                            href="<?php echo e(localized_route('pages.map')); ?>"><?php echo e(trans_db('general.kham_pha_ban_do')); ?></a>
                    </div>
                </div>
                <div
                    class="hidden lg:flex items-center justify-center">
                    <div
                        class="relative w-[350px] h-[500px] rounded-2xl shadow-2xl overflow-hidden transform rotate-3 hover:rotate-0 transition-transform duration-500 group">
                        <div
                            class="absolute inset-0 bg-gradient-to-br from-[#10B981]/80 to-[#0EA5E9]/80 opacity-20 group-hover:opacity-30 transition-opacity duration-500">
                        </div>
                        <img alt="EcoSolve App" loading="lazy" width="350" height="500" decoding="async"
                            data-nimg="1" class="object-cover" src="https://www.ecoshe.com/wp-content/uploads/2024/08/10911950-copy-2.png"
                            style="">
                    </div>
                </div>
            </div>
        </div>
        <div class="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-t from-white to-transparent"></div>
    </section>
    <section id="how-it-works" class="py-24 bg-white relative overflow-hidden">
        <div class="absolute inset-0 bg-[url('/placeholder.svg?height=100&amp;width=100')] bg-repeat opacity-5"></div>
        <div class="container mx-auto relative">
            <div class="text-center mb-16">
                <div class="inline-flex items-center rounded-full border text-xs font-semibold mb-4 bg-[#10B981]/10 text-[#10B981] border-[#10B981]/20 px-4 py-1"><?php echo e(trans_db('general.quy_trinh_cua_chung_toi')); ?></div>
                <h2 class="text-5xl font-extrabold mb-4 text-gray-900"><?php echo e(trans_db('general.cach_hoat_dong')); ?></h2>
                <p class="text-gray-700 max-w-2xl mx-auto text-lg"><?php echo e(trans_db('general.nen_tang_giup_ban_de_dang_nhan_dien_ket_noi_va_gia')); ?></p>
            </div>
            <div class="grid md:grid-cols-3 gap-8">
                <!-- Box 1 -->
                <div class="flex flex-col items-center text-center p-8 rounded-2xl shadow-md bg-white border border-gray-100">
                    <div class="w-20 h-20 rounded-full bg-green-100 flex items-center justify-center mb-6">
                        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="#10B981" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3"></path><path d="M12 9v4"></path><path d="M12 17h.01"></path></svg>
                    </div>
                    <div class="bg-gradient-to-r from-[#10B981] to-[#0EA5E9] text-white w-10 h-10 rounded-full flex items-center justify-center text-lg font-bold mb-6">1</div>
                    <h3 class="text-2xl font-bold mb-3 text-gray-900"><?php echo e(trans_db('general.bao_cao_van_de_moi_truong')); ?></h3>
                    <p class="text-gray-700 text-base"><?php echo e(trans_db('general.nhan_dien_va_ghi_nhan_cac_van_de_moi_truong_tai_kh')); ?></p>
                </div>
                <!-- Box 2 -->
                <div class="flex flex-col items-center text-center p-8 rounded-2xl shadow-md bg-white border border-gray-100">
                    <div class="w-20 h-20 rounded-full bg-green-100 flex items-center justify-center mb-6">
                        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="#10B981" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg>
                    </div>
                    <div class="bg-gradient-to-r from-[#10B981] to-[#0EA5E9] text-white w-10 h-10 rounded-full flex items-center justify-center text-lg font-bold mb-6">2</div>
                    <h3 class="text-2xl font-bold mb-3 text-gray-900"><?php echo e(trans_db('general.ket_noi_chuyen_gia')); ?></h3>
                    <p class="text-gray-700 text-base"><?php echo e(trans_db('general.nen_tang_ket_noi_ban_voi_cac_chuyen_gia_moi_truong')); ?></p>
                </div>
                <!-- Box 3 -->
                <div class="flex flex-col items-center text-center p-8 rounded-2xl shadow-md bg-white border border-gray-100">
                    <div class="w-20 h-20 rounded-full bg-green-100 flex items-center justify-center mb-6">
                        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="#10B981" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526"></path><circle cx="12" cy="8" r="6"></circle></svg>
                    </div>
                    <div class="bg-gradient-to-r from-[#10B981] to-[#0EA5E9] text-white w-10 h-10 rounded-full flex items-center justify-center text-lg font-bold mb-6">3</div>
                    <h3 class="text-2xl font-bold mb-3 text-gray-900"><?php echo e(trans_db('action.tao_tac_dong_tich_cuc')); ?></h3>
                    <p class="text-gray-700 text-base"><?php echo e(trans_db('general.cung_cong_dong_trien_khai_giai_phap_va_theo_doi_su')); ?></p>
                </div>
            </div>
        </div>
    </section>
    <section class="py-20 bg-gradient-to-br from-[#F9FAFB] to-white">
        <div class="container mx-auto">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
                <div class="p-8 rounded-2xl bg-white shadow-lg border border-gray-100 flex flex-col items-center">
                    <div class="w-16 h-16 rounded-full bg-green-100 flex items-center justify-center mb-6">
                        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="#10B981" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3"></path><path d="M12 9v4"></path><path d="M12 17h.01"></path></svg>
                    </div>
                    <div class="text-4xl md:text-5xl font-bold bg-gradient-to-r from-[#10B981] to-[#0EA5E9] bg-clip-text text-transparent mb-3">12.000+</div>
                    <p class="text-foreground text-lg font-medium"><?php echo e(trans_db('general.van_de_da_duoc_bao_cao')); ?></p>
                </div>
                <div class="p-8 rounded-2xl bg-white shadow-lg border border-gray-100 flex flex-col items-center">
                    <div class="w-16 h-16 rounded-full bg-green-100 flex items-center justify-center mb-6">
                        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="#10B981" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg>
                    </div>
                    <div class="text-4xl md:text-5xl font-bold bg-gradient-to-r from-[#10B981] to-[#0EA5E9] bg-clip-text text-transparent mb-3">500+</div>
                    <p class="text-foreground text-lg font-medium"><?php echo e(trans_db('general.cong_dong_tham_gia')); ?></p>
                </div>
                <div class="p-8 rounded-2xl bg-white shadow-lg border border-gray-100 flex flex-col items-center">
                    <div class="w-16 h-16 rounded-full bg-green-100 flex items-center justify-center mb-6">
                        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="#10B981" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><polyline points="16 11 18 13 22 9"></polyline></svg>
                    </div>
                    <div class="text-4xl md:text-5xl font-bold bg-gradient-to-r from-[#10B981] to-[#0EA5E9] bg-clip-text text-transparent mb-3">2.500+</div>
                    <p class="text-foreground text-lg font-medium"><?php echo e(trans_db('general.chuyen_gia_moi_truong')); ?></p>
                </div>
                <div class="p-8 rounded-2xl bg-white shadow-lg border border-gray-100 flex flex-col items-center">
                    <div class="w-16 h-16 rounded-full bg-green-100 flex items-center justify-center mb-6">
                        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="#10B981" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21.801 10A10 10 0 1 1 17 3.335"></path><path d="m9 11 3 3L22 4"></path></svg>
                    </div>
                    <div class="text-4xl md:text-5xl font-bold bg-gradient-to-r from-[#10B981] to-[#0EA5E9] bg-clip-text text-transparent mb-3">85%</div>
                    <p class="text-foreground text-lg font-medium"><?php echo e(trans_db('status.ty_le_giai_quyet_thanh_cong')); ?></p>
                </div>
            </div>
        </div>
    </section>
    <section id="communities" class="py-24 bg-white relative overflow-hidden">
        <div class="absolute inset-0 bg-[url('/placeholder.svg?height=100&amp;width=100')] bg-repeat opacity-5"></div>
        <div class="container mx-auto relative">
            <div class="text-center mb-16">
                <div class="inline-flex items-center rounded-full border text-xs font-semibold mb-4 bg-[#10B981]/10 text-[#10B981] border-[#10B981]/20 px-4 py-1"><?php echo e(trans_db('general.du_an_hien_tai')); ?></div>
                <h2 class="text-4xl font-bold mb-4 text-gray-900"><?php echo e(trans_db('action.tao_khac_biet_tai_dia_phuong')); ?></h2>
                <p class="text-gray-700 max-w-2xl mx-auto text-lg"><?php echo e(trans_db('general.kham_pha_cac_van_de_moi_truong_ma_cong_dong_chung')); ?></p>
            </div>
            <div class="grid md:grid-cols-3 gap-8">
                <!-- Project 1 -->
                <div class="rounded-2xl overflow-hidden border border-gray-200 shadow-md bg-white group flex flex-col">
                    <div class="relative h-56 w-full"><img alt="River cleanup" loading="lazy" decoding="async" class="object-cover w-full h-full" src="/placeholder.svg?height=200&amp;width=400" style="position: absolute; height: 100%; width: 100%; inset: 0px; color: transparent;"></div>
                    <div class="p-6 flex-1 flex flex-col justify-between">
                        <div>
                            <h3 class="font-bold text-xl mb-2 text-gray-900"><?php echo e(trans_db('general.chien_dich_lam_sach_song')); ?></h3>
                            <div class="flex items-center text-sm text-gray-600 mb-2"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#10B981" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-map-pin h-4 w-4 mr-1"><path d="M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0"></path><circle cx="12" cy="10" r="3"></circle></svg><span>Portland, Oregon</span></div>
                        </div>
                        <div class="mt-4 space-y-2">
                            <div class="flex justify-between text-sm mb-1 text-gray-600"><span><?php echo e(trans_db('general.tien_do')); ?></span><span class="font-medium">75%</span></div>
                            <div class="relative w-full overflow-hidden h-3 bg-gray-100 rounded-full">
                                <div class="h-full w-3/4 bg-gradient-to-r from-[#10B981] to-[#0EA5E9] rounded-full"></div>
                            </div>
                        </div>
                        <div class="mt-6 pt-4 border-t"><a class="text-[#10B981] font-medium text-sm inline-flex items-center hover:text-[#0EA5E9] transition-colors" href="#"><?php echo e(trans_db('action.xem_chi_tiet')); ?><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#10B981" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right h-4 w-4 ml-1"><path d="m9 18 6-6-6-6"></path></svg></a></div>
                    </div>
                </div>
                <!-- Project 2 -->
                <div class="rounded-2xl overflow-hidden border border-gray-200 shadow-md bg-white group flex flex-col">
                    <div class="relative h-56 w-full"><img alt="Urban garden" loading="lazy" decoding="async" class="object-cover w-full h-full" src="/placeholder.svg?height=200&amp;width=400" style="position: absolute; height: 100%; width: 100%; inset: 0px; color: transparent;"></div>
                    <div class="p-6 flex-1 flex flex-col justify-between">
                        <div>
                            <h3 class="font-bold text-xl mb-2 text-gray-900"><?php echo e(trans_db('general.du_an_vuon_cong_dong')); ?></h3>
                            <div class="flex items-center text-sm text-gray-600 mb-2"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#10B981" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-map-pin h-4 w-4 mr-1"><path d="M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0"></path><circle cx="12" cy="10" r="3"></circle></svg><span>Austin, Texas</span></div>
                        </div>
                        <div class="mt-4 space-y-2">
                            <div class="flex justify-between text-sm mb-1 text-gray-600"><span><?php echo e(trans_db('general.tien_do')); ?></span><span class="font-medium">45%</span></div>
                            <div class="relative w-full overflow-hidden h-3 bg-gray-100 rounded-full">
                                <div class="h-full w-[45%] bg-gradient-to-r from-[#10B981] to-[#0EA5E9] rounded-full"></div>
                            </div>
                        </div>
                        <div class="mt-6 pt-4 border-t"><a class="text-[#10B981] font-medium text-sm inline-flex items-center hover:text-[#0EA5E9] transition-colors" href="#"><?php echo e(trans_db('action.xem_chi_tiet')); ?><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#10B981" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right h-4 w-4 ml-1"><path d="m9 18 6-6-6-6"></path></svg></a></div>
                    </div>
                </div>
                <!-- Project 3 -->
                <div class="rounded-2xl overflow-hidden border border-gray-200 shadow-md bg-white group flex flex-col">
                    <div class="relative h-56 w-full"><img alt="Recycling program" loading="lazy" decoding="async" class="object-cover w-full h-full" src="/placeholder.svg?height=200&amp;width=400" style="position: absolute; height: 100%; width: 100%; inset: 0px; color: transparent;"></div>
                    <div class="p-6 flex-1 flex flex-col justify-between">
                        <div>
                            <h3 class="font-bold text-xl mb-2 text-gray-900"><?php echo e(trans_db('general.chuong_trinh_tai_che_khu_pho')); ?></h3>
                            <div class="flex items-center text-sm text-gray-600 mb-2"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#10B981" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-map-pin h-4 w-4 mr-1"><path d="M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0"></path><circle cx="12" cy="10" r="3"></circle></svg><span>Chicago, Illinois</span></div>
                        </div>
                        <div class="mt-4 space-y-2">
                            <div class="flex justify-between text-sm mb-1 text-gray-600"><span><?php echo e(trans_db('general.tien_do')); ?></span><span class="font-medium">90%</span></div>
                            <div class="relative w-full overflow-hidden h-3 bg-gray-100 rounded-full">
                                <div class="h-full w-[90%] bg-gradient-to-r from-[#10B981] to-[#0EA5E9] rounded-full"></div>
                            </div>
                        </div>
                        <div class="mt-6 pt-4 border-t"><a class="text-[#10B981] font-medium text-sm inline-flex items-center hover:text-[#0EA5E9] transition-colors" href="#"><?php echo e(trans_db('action.xem_chi_tiet')); ?><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#10B981" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right h-4 w-4 ml-1"><path d="m9 18 6-6-6-6"></path></svg></a></div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section id="experts" class="py-24 bg-gradient-to-br from-[#F9FAFB] to-white relative overflow-hidden">
        <div class="absolute inset-0 z-0 opacity-5"><svg class="absolute right-0 top-0 h-full w-full" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg"><path fill="#10B981" d="M37.5,-65.1C48.9,-55.3,58.9,-44.9,67.1,-32.2C75.3,-19.5,81.7,-4.6,79.8,9.2C77.9,23,67.8,35.6,56.4,44.9C45,54.2,32.4,60.2,18.8,65.2C5.2,70.2,-9.3,74.3,-22.6,71.7C-35.9,69.1,-47.9,59.9,-58.3,48.2C-68.7,36.5,-77.5,22.4,-79.2,7.3C-80.9,-7.8,-75.5,-23.8,-66.6,-36.9C-57.7,-50,-45.3,-60.2,-32,-67.4C-18.7,-74.6,-4.7,-78.9,7.9,-75.5C20.5,-72.1,41,-74.9,37.5,-65.1Z" transform="translate(100 100)"></path></svg></div>
        <div class="container mx-auto relative z-10">
            <div class="text-center mb-16">
                <div class="inline-flex items-center rounded-full border text-xs font-semibold mb-4 bg-[#10B981]/10 text-[#10B981] border-[#10B981]/20 px-4 py-1"><?php echo e(trans_db('general.cam_nhan_cong_dong')); ?></div>
                <h2 class="text-4xl font-bold mb-4 text-gray-900"><?php echo e(trans_db('general.cam_nhan_tu_cong_dong')); ?></h2>
                <p class="text-gray-700 max-w-2xl mx-auto text-lg"><?php echo e(trans_db('action.lang_nghe_chia_se_tu_nhung_nguoi_dang_tao_ra_su_th')); ?></p>
            </div>
            <div class="grid md:grid-cols-3 gap-8">
                <!-- Testimonial 1 -->
                <div class="bg-white p-8 rounded-2xl shadow-md border border-gray-100 flex flex-col relative">
                    <div class="absolute top-0 right-0 w-20 h-20 bg-[#10B981]/5 rounded-bl-3xl"></div>
                    <div class="flex items-center mb-6 relative z-10">
                        <div class="relative w-16 h-16 rounded-full overflow-hidden mr-4 border-2 border-[#10B981]/20 shadow-md">
                            <img alt="Sarah Johnson" loading="lazy" decoding="async" class="object-cover w-full h-full" src="/placeholder.svg?height=50&amp;width=50">
                        </div>
                        <div>
                            <h4 class="font-bold text-lg text-gray-900">Sarah Johnson</h4>
                            <p class="text-sm text-gray-600"><?php echo e(trans_db('general.truong_nhom_cong_dong')); ?></p>
                        </div>
                    </div>
                    <div class="relative z-10">
                        <svg class="absolute -top-2 -left-2 h-8 w-8 text-[#10B981]/20" fill="currentColor" viewBox="0 0 32 32"><path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z"></path></svg>
                        <p class="italic text-gray-700 text-lg relative"><?php echo e(trans_db('general.ecosolve_da_giup_khu_pho_cua_chung_toi_giai_quyet')); ?></p>
                    </div>
                </div>
                <!-- Testimonial 2 -->
                <div class="bg-white p-8 rounded-2xl shadow-md border border-gray-100 flex flex-col relative">
                    <div class="absolute top-0 right-0 w-20 h-20 bg-[#10B981]/5 rounded-bl-3xl"></div>
                    <div class="flex items-center mb-6 relative z-10">
                        <div class="relative w-16 h-16 rounded-full overflow-hidden mr-4 border-2 border-[#10B981]/20 shadow-md">
                            <img alt="Michael Chen" loading="lazy" decoding="async" class="object-cover w-full h-full" src="/placeholder.svg?height=50&amp;width=50">
                        </div>
                        <div>
                            <h4 class="font-bold text-lg text-gray-900">Michael Chen</h4>
                            <p class="text-sm text-gray-600"><?php echo e(trans_db('general.chuyen_gia_moi_truong')); ?></p>
                        </div>
                    </div>
                    <div class="relative z-10">
                        <svg class="absolute -top-2 -left-2 h-8 w-8 text-[#10B981]/20" fill="currentColor" viewBox="0 0 32 32"><path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z"></path></svg>
                        <p class="italic text-gray-700 text-lg relative"><?php echo e(trans_db('general.la_chuyen_gia_tren_nen_tang_toi_da_co_co_hoi_ho_tr')); ?></p>
                    </div>
                </div>
                <!-- Testimonial 3 -->
                <div class="bg-white p-8 rounded-2xl shadow-md border border-gray-100 flex flex-col relative">
                    <div class="absolute top-0 right-0 w-20 h-20 bg-[#10B981]/5 rounded-bl-3xl"></div>
                    <div class="flex items-center mb-6 relative z-10">
                        <div class="relative w-16 h-16 rounded-full overflow-hidden mr-4 border-2 border-[#10B981]/20 shadow-md">
                            <img alt="Elena Rodriguez" loading="lazy" decoding="async" class="object-cover w-full h-full" src="/placeholder.svg?height=50&amp;width=50">
                        </div>
                        <div>
                            <h4 class="font-bold text-lg text-gray-900">Elena Rodriguez</h4>
                            <p class="text-sm text-gray-600"><?php echo e(trans_db('general.thanh_vien_hoi_dong_thanh_pho')); ?></p>
                        </div>
                    </div>
                    <div class="relative z-10">
                        <svg class="absolute -top-2 -left-2 h-8 w-8 text-[#10B981]/20" fill="currentColor" viewBox="0 0 32 32"><path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z"></path></svg>
                        <p class="italic text-gray-700 text-lg relative"><?php echo e(trans_db('general.ecosolve_da_thay_doi_cach_thanh_pho_chung_toi_tiep')); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section id="about"
        class="py-24 bg-gradient-to-br from-[#10B981] to-[#0EA5E9] text-white relative overflow-hidden">
        <div class="absolute inset-0 bg-[url('/placeholder.svg?height=100&amp;width=100')] bg-repeat opacity-5"></div>
        <div class="absolute top-0 right-0 w-1/3 h-full bg-white/5 skew-x-12 -translate-x-20"></div>
        <div class="absolute bottom-0 left-0 w-1/3 h-full bg-white/5 -skew-x-12 translate-x-20"></div>
        <div class="container mx-auto relative z-10">
            <div class="max-w-3xl mx-auto text-center">
                <div class="inline-flex items-center rounded-full border text-xs font-semibold mb-6 bg-white/20 text-white border-white/20 px-4 py-1 backdrop-blur-sm">
                    <?php echo e(trans_db('common.tham_gia_ngay')); ?></div>
                <h2 class="text-4xl font-bold mb-6"><?php echo e(trans_db('action.san_sang_tao_tac_dong')); ?></h2>
                <p class="text-white/90 mb-10 text-xl"><?php echo e(trans_db('action.hay_cung_hang_ngan_nguoi_tao_ra_thay_doi_ben_vung')); ?></p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="<?php echo e(localized_route('forum.index')); ?>" class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md font-medium bg-white text-[#10B981] hover:bg-white/90 hover:text-[#10B981] text-lg px-8 py-6 h-auto shadow-lg"><?php echo e(trans_db('general.tham_gia_cong_dong')); ?></a>
                    <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md font-medium border bg-background border-white text-white hover:bg-white/10 text-lg px-8 py-6 h-auto"><?php echo e(trans_db('general.tim_hieu_them')); ?></button>
                </div>
            </div>
        </div>
    </section>
    <section class="py-24 bg-white">
        <div class="container mx-auto">
            <div class="text-center mb-16">
                <div class="inline-flex items-center rounded-full border text-xs font-semibold mb-4 bg-[#10B981]/10 text-[#10B981] border-[#10B981]/20 px-4 py-1"><?php echo e(trans_db('general.cach_tiep_can_cua_chung_toi')); ?></div>
                <h2 class="text-4xl font-bold mb-4 text-gray-900"><?php echo e(trans_db('general.vi_sao_chon_ecosolve')); ?></h2>
                <p class="text-gray-700 max-w-2xl mx-auto text-lg"><?php echo e(trans_db('general.nen_tang_cung_cap_cac_tinh_nang_doc_dao_giup_toi_d')); ?></p>
            </div>
            <div class="grid md:grid-cols-3 gap-8">
                <!-- Feature 1 -->
                <div class="p-6 rounded-2xl border border-gray-100 shadow-md bg-white flex flex-col items-center">
                    <div class="w-14 h-14 rounded-full bg-green-100 flex items-center justify-center mb-6">
                        <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="#10B981" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z"></path><path d="M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12"></path></svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3 text-gray-900"><?php echo e(trans_db('general.tap_trung_dia_phuong')); ?></h3>
                    <p class="text-gray-700"><?php echo e(trans_db('general.chung_toi_tin_rang_thay_doi_moi_truong_ben_vung_ba')); ?></p>
                </div>
                <!-- Feature 2 -->
                <div class="p-6 rounded-2xl border border-gray-100 shadow-md bg-white flex flex-col items-center">
                    <div class="w-14 h-14 rounded-full bg-green-100 flex items-center justify-center mb-6">
                        <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="#10B981" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M7 16.3c2.2 0 4-1.83 4-4.05 0-1.16-.57-2.26-1.71-3.19S7.29 6.75 7 5.3c-.29 1.45-1.14 2.84-2.29 3.76S3 11.1 3 12.25c0 2.22 1.8 4.05 4 4.05z"></path><path d="M12.56 6.6A10.97 10.97 0 0 0 14 3.02c.5 2.5 2 4.9 4 6.5s3 3.5 3 5.5a6.98 6.98 0 0 1-11.91 4.97"></path></svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3 text-gray-900"><?php echo e(trans_db('general.huong_dan_tu_chuyen_gia')); ?></h3>
                    <p class="text-gray-700"><?php echo e(trans_db('general.mang_luoi_chuyen_gia_moi_truong_cua_chung_toi_cung')); ?></p>
                </div>
                <!-- Feature 3 -->
                <div class="p-6 rounded-2xl border border-gray-100 shadow-md bg-white flex flex-col items-center">
                    <div class="w-14 h-14 rounded-full bg-green-100 flex items-center justify-center mb-6">
                        <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="#10B981" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M7 19H4.815a1.83 1.83 0 0 1-1.57-.881 1.785 1.785 0 0 1-.004-1.784L7.196 9.5"></path><path d="M11 19h8.203a1.83 1.83 0 0 0 1.556-.89 1.784 1.784 0 0 0 0-1.775l-1.226-2.12"></path><path d="m14 16-3 3 3 3"></path><path d="M8.293 13.596 7.196 9.5 3.1 10.598"></path><path d="m9.344 5.811 1.093-1.892A1.83 1.83 0 0 1 11.985 3a1.784 1.784 0 0 1 1.546.888l3.943 6.843"></path><path d="m13.378 9.633 4.096 1.098 1.097-4.096"></path></svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3 text-gray-900"><?php echo e(trans_db('general.tac_dong_do_luong_duoc')); ?></h3>
                    <p class="text-gray-700"><?php echo e(trans_db('time.theo_doi_tien_do_voi_du_lieu_thoi_gian_thuc_va_nhi')); ?></p>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH F:\backend-app_don_rac\resources\views/home.blade.php ENDPATH**/ ?>
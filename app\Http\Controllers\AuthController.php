<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use App\Models\Account;
use Illuminate\Auth\Events\Verified;
use App\Http\Requests\AccountEmailVerificationRequest;

class AuthController extends Controller
{
    public function showRegister()
    {
        return view('auth.register');
    }

    public function register(Request $request)
    {
        $request->validate([
            'email' => ['required', 'string', 'email', 'max:255', 'unique:accounts'],
            'username' => ['required', 'string', 'max:255', 'unique:accounts'],
            'full_name' => ['required', 'string', 'max:255'],
            'password' => ['required', 'confirmed', 'min:8'],
        ]);

        $account = Account::create([
            'email' => $request->email,
            'username' => $request->username,
            'full_name' => $request->full_name,
            'password' => Hash::make($request->password),
        ]);

        Auth::guard('account')->login($account);

        // Đảm bảo locale được đặt trong URL
        app()->setLocale($request->route('locale'));
        
        // Send email verification notification
        $account->sendEmailVerificationNotification();

        return redirect()->route('verification.notice', ['locale' => app()->getLocale()]);
    }

    public function showLogin()
    {
        return view('auth.login');
    }

    public function login(Request $request)
    {
        \Log::info('Login attempt started', [
            'email' => $request->input('email'),
            'has_password' => !empty($request->input('password')),
            'remember' => $request->filled('remember')
        ]);

        $request->validate([
            'email' => 'required|string',
            'password' => 'required|string',
        ]);

        $login = $request->input('email');
        $password = $request->input('password');
        $remember = $request->filled('remember');

        // Determine if login is email or username
        $field = filter_var($login, FILTER_VALIDATE_EMAIL) ? 'email' : 'username';

        $credentials = [
            $field => $login,
            'password' => $password,
        ];

        \Log::info('Login credentials prepared', [
            'field' => $field,
            'login' => $login,
            'credentials_field' => $field
        ]);

        if (Auth::guard('account')->attempt($credentials, $remember)) {
            \Log::info('Login successful', ['user_id' => Auth::guard('account')->id()]);
            $request->session()->regenerate();
            return redirect()->route('home', ['locale' => app()->getLocale()]);
        }

        \Log::warning('Login failed', [
            'field' => $field,
            'login' => $login
        ]);

        return back()->withErrors([
            'email' => 'Thông tin đăng nhập không chính xác.',
        ])->withInput($request->except('password'));
    }

    public function logout(Request $request)
    {
        Auth::guard('account')->logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        return redirect()->route('login', ['locale' => app()->getLocale()]);
    }

    public function showVerifyEmail()
    {
        return view('auth.verify-email');
    }

    public function verifyEmail(AccountEmailVerificationRequest $request)
    {
        $request->fulfill();

        return redirect()->route('dashboard', ['locale' => app()->getLocale()]);
    }

    public function sendVerificationEmail(Request $request)
    {
        $request->user('account')->sendEmailVerificationNotification();

        return back()->with('message', 'Verification link sent!');
    }

    public function showResetPassword(Request $request)
    {
        $token = $request->query('token');
        $email = $request->query('email');
        return view('auth.reset-password', compact('token', 'email'));
    }

    public function resetPassword(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'token' => 'required',
            'password' => 'required|confirmed|min:6',
        ]);

        $passwordReset = DB::table('password_resets')
            ->where('email', $request->email)
            ->where('token', $request->token)
            ->first();

        if (!$passwordReset) {
            return back()->withErrors(['email' => 'Link đặt lại mật khẩu không hợp lệ hoặc đã hết hạn.'])->withInput();
        }

        $account = \App\Models\Account::where('email', $request->email)->first();
        if (!$account) {
            return back()->withErrors(['email' => 'Không tìm thấy tài khoản.'])->withInput();
        }

        $account->password = Hash::make($request->password);
        $account->save();

        DB::table('password_resets')->where('email', $request->email)->delete();

        return redirect('/login')->with('status', 'Đặt lại mật khẩu thành công! Bạn có thể đăng nhập bằng mật khẩu mới.');
    }
}



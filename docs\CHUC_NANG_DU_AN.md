# Chức năng dự án EcoSolves

## 1. <PERSON><PERSON><PERSON> năng người dùng
- <PERSON><PERSON><PERSON> ký, đ<PERSON><PERSON>h<PERSON>, đăng xuất tài kho<PERSON>n
- <PERSON><PERSON>, chỉnh sửa thông tin cá nhân, đổi mật khẩu, x<PERSON><PERSON> tà<PERSON> kho<PERSON>n
- <PERSON><PERSON> danh sách, chi ti<PERSON>t cá<PERSON> bà<PERSON> viết, b<PERSON><PERSON> viết nổi bật, bài viết theo danh mục
- <PERSON><PERSON>, t<PERSON><PERSON>, trả lời chủ đề diễn đàn (forum), xem chi tiết chủ đề và phản hồi lồng nhau
- <PERSON>em, tạo báo cáo điểm rác (g<PERSON><PERSON>nh, mô tả, vị trí), xem bản đồ các điểm rác
- Xem, đăng ký tham gia sự kiện, xem chi tiết sự kiện
- Xem, đổi quà tặng bằng điểm tí<PERSON> lũ<PERSON>, xem lịch sử đổi quà
- <PERSON>em thống kê cá nhân: tổ<PERSON> b<PERSON><PERSON>, b<PERSON><PERSON> c<PERSON><PERSON> đ<PERSON> du<PERSON>, đang chờ, tổ<PERSON> lượt đổi quà, điểm đã tích lũy/đã sử dụng
- Gửi liên hệ qua form liên hệ
- Đổi ngôn ngữ giao diện (vi/en)

## 2. Chức năng API
- Đăng ký tài khoản qua email (gửi mã xác thực, xác thực mã, hoàn tất đăng ký)
- Đăng nhập, đăng xuất tài khoản
- Cập nhật thông tin cá nhân, avatar
- Gửi báo cáo điểm rác (ảnh, mô tả, vị trí)
- Đăng ký tham gia sự kiện
- Tạo chủ đề và trả lời chủ đề diễn đàn
- Đổi quà tặng, xem lịch sử đổi quà

## 3. Chức năng quản trị (Admin/Filament)
- Quản lý tài khoản người dùng (Account, User): tạo, sửa, xóa, kích hoạt/vô hiệu hóa
- Quản lý bài viết (Post): tạo, sửa, xóa, gán danh mục, tag, ảnh đại diện, nổi bật, ngày xuất bản
- Quản lý danh mục (Category): tạo, sửa, xóa, upload ảnh, mô tả
- Quản lý tag: tạo, sửa, xóa
- Quản lý báo cáo điểm rác: duyệt, từ chối, xem chi tiết, lọc trạng thái, xem vị trí
- Quản lý sự kiện: tạo, sửa, xóa, duyệt, từ chối, đặt số lượng tham gia, ảnh đại diện
- Quản lý quà tặng: tạo, sửa, xóa, đặt số lượng, điểm quy đổi, thời gian hiệu lực, ảnh
- Quản lý đổi quà: duyệt, từ chối, hoàn thành, hoàn điểm/quà khi từ chối, ghi chú admin
- Quản lý bình luận/tin nhắn: duyệt, từ chối, xóa, lọc theo loại nội dung
- Quản lý thông tin cấu hình hệ thống (site settings): tên site, mô tả, logo, favicon, thông tin liên hệ, mạng xã hội, SEO

## 4. Chức năng hệ thống/phụ trợ
- Đa ngôn ngữ (vi/en)
- Gửi email xác thực, thông báo
- Lưu cache cấu hình hệ thống
- Quản lý session, bảo mật xác thực
- Ghi log lỗi hệ thống
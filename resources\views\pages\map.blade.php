@extends('layouts.app')

@section('title', trans_db('general.ban_do_diem_rac') ?? 'Bản đồ điểm rác')

@section('header', trans_db('general.ban_do_diem_rac_tap_trung') ?? '<PERSON><PERSON>n đồ điểm rác tập trung')

@section('content')
    <div class="container mx-auto py-8">
        <!-- Leaflet CSS -->
        <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />

        <h2 class="text-2xl font-bold text-center mb-4 text-[#10B981]">
            {{ trans_db('general.ban_do_diem_rac_tap_trung') ?? 'Bản đồ điểm rác tập trung' }}
        </h2>

        <div class="flex flex-col md:flex-row gap-4">
            <!-- <PERSON><PERSON> sách điểm rác -->
            <div class="w-full md:w-1/3 max-h-[70vh] overflow-y-auto bg-white rounded-lg shadow p-4">
                <h3 class="text-lg font-semibold mb-3 text-[#10B981]">
                    {{ trans_db('general.danh_sach_diem_rac') ?? 'Danh sách điểm rác' }}
                </h3>
                <ul id="report-list">
                    @foreach ($reports as $i => $r)
                        <li class="border-b pb-2 last:border-b-0 cursor-pointer hover:bg-[#f0fdfa] rounded transition" data-index="{{ $i }}">
                            <div class="flex items-center gap-2">
                                <span class="font-bold text-[#0EA5E9]">#{{ $i + 1 }}</span>
                                <span class="text-gray-700">{{ Str::limit($r['description'], 40) }}</span>
                            </div>
                            @if ($r['image'])
                                <img src="/storage/{{ $r['image'] }}" alt="{{ trans_db('general.anh_diem_rac') ?? 'Ảnh điểm rác' }}"
                                    class="my-1 rounded max-w-[120px] max-h-[80px]">
                            @endif
                            <div class="text-xs text-gray-500">
                                {{ trans_db('general.vi_do') ?? 'Vĩ độ' }}: {{ $r['latitude'] }},
                                {{ trans_db('general.kinh_do') ?? 'Kinh độ' }}: {{ $r['longitude'] }}
                            </div>
                            @if ($r['gmap_link'])
                                <a href="{{ $r['gmap_link'] }}" target="_blank" class="text-xs text-[#10B981] hover:underline">
                                    {{ trans_db('action.xem_tren_google_maps') ?? 'Xem trên Google Maps' }}
                                </a>
                            @endif
                        </li>
                    @endforeach
                </ul>
            </div>

            <!-- Bản đồ -->
            <div class="w-full md:w-2/3 flex items-center justify-center">                 <!-- Map container -->
                <div id="map" style="height: 450px; width: 100%; border-radius: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);"></div>

                <!-- Google Maps iframe (hidden by default) -->
                <iframe id="gmap-iframe" src="" width="100%" height="450"
                    style="border:0; display:none; min-height:350px; border-radius:12px; box-shadow:0 2px 8px rgba(0,0,0,0.1);"
                    allowfullscreen="" loading="lazy"></iframe>

                <!-- No map message -->
                <div id="no-map-msg" class="text-center text-gray-500 py-8" style="display:none;">
                    {{ trans_db('general.khong_co_ban_do') ?? 'Không có bản đồ để hiển thị' }}
                </div>
            </div>
        </div>
    </div>

    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // Dữ liệu reports từ PHP
        var reports = @json($reports ?? []);

        // Khởi tạo bản đồ Leaflet
        var map = L.map('map').setView([10.8231, 106.6297], 10); // Default to Ho Chi Minh City

        // Thêm tile layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© {{ trans_db("general.openstreetmap") ?? "OpenStreetMap" }}'
        }).addTo(map);
        // Tạo marker và popup cho từng báo cáo
        var markers = [];
        reports.forEach(function(r, idx) {
            if (!r.latitude || !r.longitude) return;

            var popupHtml = `
                <div style='min-width:180px'>
                    <strong>{{ trans_db('general.mo_ta') ?? 'Mô tả' }}:</strong><br>
                    ${r.description ? r.description : '{{ trans_db('general.khong_co_mo_ta') ?? 'Không có mô tả' }}'}<br>
                    ${r.image ? `<img src='/storage/${r.image}' alt="{{ trans_db('general.anh_diem_rac') ?? 'Ảnh điểm rác' }}" style='max-width:150px; margin:8px 0; border-radius:4px;'>` : ''}
                    ${r.gmap_link ? `<br><a href='${r.gmap_link}' target='_blank' style='color:#10B981;'>{{ trans_db('action.xem_tren_google_maps') ?? 'Xem trên Google Maps' }}</a>` : ''}
                </div>
            `;

            var marker = L.marker([r.latitude, r.longitude])
                .addTo(map)
                .bindPopup(popupHtml);
            markers.push(marker);
        });

        // Xử lý click vào danh sách
        const iframe = document.getElementById('gmap-iframe');
        const noMapMsg = document.getElementById('no-map-msg');

        document.querySelectorAll('#report-list li').forEach(function(item) {
            item.addEventListener('click', function() {
                var idx = parseInt(this.getAttribute('data-index'));
                var r = reports[idx];

                // Focus marker trên Leaflet
                if (markers[idx]) {
                    map.setView([r.latitude, r.longitude], 16);
                    markers[idx].openPopup();
                }

                // Hiện Google Maps iframe nếu có link
                if (r.gmap_link) {
                    iframe.src = r.gmap_link;
                    iframe.style.display = 'block';
                    document.getElementById('map').style.display = 'none';
                } else {
                    iframe.src = '';
                    iframe.style.display = 'none';
                    document.getElementById('map').style.display = 'block';
                }
            });
        });

        // Nếu không có reports, hiện thông báo
        if (reports.length === 0) {
            document.getElementById('map').style.display = 'none';
            noMapMsg.style.display = 'block';
        }
    </script>
@endsection

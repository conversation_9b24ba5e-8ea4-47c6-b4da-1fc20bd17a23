<?php

namespace App\Http\Controllers;

use App\Models\Gift;
use App\Models\GiftRedemption;
use App\Models\PointHistory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class GiftController extends Controller
{
    public function redeem(Request $request)
    {
        $request->validate([
            'gift_id' => 'required|exists:gifts,id',
            'quantity' => 'required|integer|min:1|max:10',
        ]);

        $account = Auth::guard('account')->user();
        
        if (!$account) {
            return response()->json([
                'success' => false,
                'message' => 'Vui lòng đăng nhập để đổi quà!'
            ], 401);
        }

        try {
            DB::beginTransaction();

            $gift = Gift::findOrFail($request->gift_id);
            
            // Kiểm tra quà tặng có hoạt động không
            if (!$gift->is_active) {
                return response()->json([
                    'success' => false,
                    'message' => 'Quà tặng này hiện không khả dụng!'
                ], 400);
            }

            // Kiểm tra thời gian hiệu lực
            $now = now();
            if ($gift->available_from > $now || $gift->available_until < $now) {
                return response()->json([
                    'success' => false,
                    'message' => 'Quà tặng này chưa hoặc đã hết hạn!'
                ], 400);
            }

            // Kiểm tra số lượng còn lại
            if ($gift->quantity < $request->quantity) {
                return response()->json([
                    'success' => false,
                    'message' => 'Số lượng quà tặng không đủ!'
                ], 400);
            }

            // Tính tổng điểm cần thiết
            $totalPointsNeeded = $gift->value * $request->quantity;

            // Kiểm tra điểm của người dùng
            if ($account->point < $totalPointsNeeded) {
                return response()->json([
                    'success' => false,
                    'message' => 'Điểm tích lũy không đủ để đổi quà này!'
                ], 400);
            }

            // Trừ điểm từ tài khoản
            $account->point -= $totalPointsNeeded;
            $account->save();

            // Giảm số lượng quà tặng
            $gift->quantity -= $request->quantity;
            $gift->save();

            // Tạo bản ghi lịch sử tích điểm (trừ điểm)
            PointHistory::create([
                'account_id' => $account->id,
                'change' => -$totalPointsNeeded,
                'description' => "Đổi quà: {$gift->name} (x{$request->quantity})",
            ]);

            // Tạo bản ghi đổi quà
            GiftRedemption::create([
                'account_id' => $account->id,
                'gift_id' => $gift->id,
                'quantity' => $request->quantity,
                'point_used' => $totalPointsNeeded,
                'status' => 'pending',
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Đổi quà thành công! Vui lòng chờ admin xác nhận.',
                'remaining_points' => $account->point,
                'gift_name' => $gift->name,
                'quantity' => $request->quantity,
                'points_used' => $totalPointsNeeded,
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            \Log::error('Gift redemption error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi đổi quà! Vui lòng thử lại.'
            ], 500);
        }
    }

    public function history()
    {
        $account = Auth::guard('account')->user();
        
        if (!$account) {
            return redirect()->route('login');
        }

        $redemptions = GiftRedemption::with('gift')
            ->where('account_id', $account->id)
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('gifts.history', compact('redemptions'));
    }
}

<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Translation;

class EmailVerificationTranslationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $translations = [
            'email.xac_thuc_email' => [
                'vi' => '<PERSON><PERSON><PERSON> thực Email',
                'en' => 'Verify Email'
            ],
            'email.cam_on_ban_da_dang_ky_tai_khoan' => [
                'vi' => 'Cảm ơn bạn đã đăng ký tài khoản',
                'en' => 'Thank you for registering an account'
            ],
            'email.vui_long_nhan_vao_nut_de_xac_thuc_email' => [
                'vi' => 'Vui lòng nhấn vào nút bên dưới để xác thực địa chỉ email của bạn',
                'en' => 'Please click the button below to verify your email address'
            ],
            'email.vuil_long_xac_thuc_email' => [
                'vi' => 'Vui lòng xác thực email',
                'en' => 'Please verify your email'
            ],
            'email.lien_ket_da_duoc_gui_toi_email_cua_ban' => [
                'vi' => 'Liên kết xác thực đã được gửi tới email của bạn.',
                'en' => 'A verification link has been sent to your email.'
            ],
            'email.neu_chua_nhan_duoc_yeu_cau_gui_lai' => [
                'vi' => 'Nếu chưa nhận được, bạn có thể yêu cầu gửi lại.',
                'en' => 'If you haven\'t received it, you can request to resend.'
            ],
            'auth.resend_verification' => [
                'vi' => 'Gửi lại email xác thực',
                'en' => 'Resend verification email'
            ]
        ];

        foreach ($translations as $key => $values) {
            foreach ($values as $locale => $value) {
                Translation::updateOrCreate(
                    ['key' => $key, 'locale' => $locale],
                    ['value' => $value]
                );
            }
        }

        $this->command->info('Email verification translations seeded successfully.');
    }
}

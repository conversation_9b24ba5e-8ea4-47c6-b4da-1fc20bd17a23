<?php

namespace App\Filament\Resources;

use App\Filament\Resources\GiftRedemptionResource\Pages;
use App\Filament\Resources\GiftRedemptionResource\RelationManagers;
use App\Models\GiftRedemption;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Actions\Action;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class GiftRedemptionResource extends Resource
{
    protected static ?string $model = GiftRedemption::class;

    protected static ?string $navigationIcon = 'heroicon-o-ticket';
    
    protected static ?string $navigationGroup = 'Content Management';
    
    protected static ?string $navigationLabel = 'Gift Redemptions';
    
    protected static ?string $modelLabel = 'Gift Redemption';
    
    protected static ?string $pluralModelLabel = 'Gift Redemptions';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Redemption Information')
                    ->schema([
                        Forms\Components\Select::make('account_id')
                            ->relationship('account', 'full_name')
                            ->searchable()
                            ->required()
                            ->disabled(),
                        Forms\Components\Select::make('gift_id')
                            ->relationship('gift', 'name')
                            ->searchable()
                            ->required()
                            ->disabled(),
                        Forms\Components\TextInput::make('quantity')
                            ->numeric()
                            ->required()
                            ->disabled(),
                        Forms\Components\TextInput::make('point_used')
                            ->numeric()
                            ->required()
                            ->disabled()
                            ->label('Points Used'),
                    ])->columns(2),
                    
                Forms\Components\Section::make('Status Management')
                    ->schema([
                        Forms\Components\Select::make('status')
                            ->options([
                                'pending' => 'Pending',
                                'approved' => 'Approved',
                                'rejected' => 'Rejected',
                                'completed' => 'Completed',
                            ])
                            ->required()
                            ->default('pending'),
                        Forms\Components\Textarea::make('notes')
                            ->maxLength(1000)
                            ->label('Admin Notes')
                            ->placeholder('Add notes about this redemption...'),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('account.full_name')
                    ->searchable()
                    ->sortable()
                    ->label('Customer'),
                Tables\Columns\TextColumn::make('gift.name')
                    ->searchable()
                    ->sortable()
                    ->label('Gift'),
                Tables\Columns\TextColumn::make('quantity')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('point_used')
                    ->numeric()
                    ->sortable()
                    ->label('Points Used'),
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'pending' => 'warning',
                        'approved' => 'success',
                        'rejected' => 'danger',
                        'completed' => 'info',
                    }),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->label('Requested At'),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->label('Last Updated'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'pending' => 'Pending',
                        'approved' => 'Approved',
                        'rejected' => 'Rejected',
                        'completed' => 'Completed',
                    ]),
                Tables\Filters\Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('created_from'),
                        Forms\Components\DatePicker::make('created_until'),
                    ])
                    ->query(function ($query, array $data) {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn ($query) => $query->whereDate('created_at', '>=', $data['created_from'])
                            )
                            ->when(
                                $data['created_until'],
                                fn ($query) => $query->whereDate('created_at', '<=', $data['created_until'])
                            );
                    })
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Action::make('approve')
                    ->label('Approve')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->requiresConfirmation()
                    ->modalHeading('Approve Gift Redemption')
                    ->modalDescription('Are you sure you want to approve this gift redemption?')
                    ->modalSubmitActionLabel('Yes, approve')
                    ->visible(fn (GiftRedemption $record) => $record->status === 'pending')
                    ->action(function (GiftRedemption $record) {
                        $record->update(['status' => 'approved']);
                        
                        Notification::make()
                            ->title('Redemption Approved')
                            ->success()
                            ->send();
                    }),
                Action::make('reject')
                    ->label('Reject')
                    ->icon('heroicon-o-x-circle')
                    ->color('danger')
                    ->requiresConfirmation()
                    ->modalHeading('Reject Gift Redemption')
                    ->modalDescription('Are you sure you want to reject this gift redemption?')
                    ->modalSubmitActionLabel('Yes, reject')
                    ->visible(fn (GiftRedemption $record) => $record->status === 'pending')
                    ->form([
                        Forms\Components\Textarea::make('rejection_reason')
                            ->label('Rejection Reason')
                            ->required()
                            ->placeholder('Please provide a reason for rejection...'),
                    ])
                    ->action(function (GiftRedemption $record, array $data) {
                        $record->update([
                            'status' => 'rejected',
                            'notes' => $data['rejection_reason']
                        ]);
                        
                        // Refund points to the account
                        $account = $record->account;
                        $account->point += $record->point_used;
                        $account->save();
                        
                        // Restore gift quantity
                        $gift = $record->gift;
                        $gift->quantity += $record->quantity;
                        $gift->save();
                        
                        Notification::make()
                            ->title('Redemption Rejected')
                            ->body('Points have been refunded and gift quantity restored.')
                            ->success()
                            ->send();
                    }),
                Action::make('complete')
                    ->label('Mark Complete')
                    ->icon('heroicon-o-flag')
                    ->color('info')
                    ->requiresConfirmation()
                    ->modalHeading('Mark Redemption Complete')
                    ->modalDescription('Are you sure you want to mark this redemption as completed?')
                    ->modalSubmitActionLabel('Yes, complete')
                    ->visible(fn (GiftRedemption $record) => $record->status === 'approved')
                    ->action(function (GiftRedemption $record) {
                        $record->update(['status' => 'completed']);
                        
                        Notification::make()
                            ->title('Redemption Completed')
                            ->success()
                            ->send();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Action::make('bulk_approve')
                        ->label('Approve Selected')
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->requiresConfirmation()
                        ->modalHeading('Approve Multiple Redemptions')
                        ->modalDescription('Are you sure you want to approve all selected redemptions?')
                        ->modalSubmitActionLabel('Yes, approve all')
                        ->action(function ($records) {
                            $records->each(function ($record) {
                                if ($record->status === 'pending') {
                                    $record->update(['status' => 'approved']);
                                }
                            });
                            
                            Notification::make()
                                ->title('Redemptions Approved')
                                ->success()
                                ->send();
                        }),
                    Action::make('bulk_reject')
                        ->label('Reject Selected')
                        ->icon('heroicon-o-x-circle')
                        ->color('danger')
                        ->requiresConfirmation()
                        ->modalHeading('Reject Multiple Redemptions')
                        ->modalDescription('Are you sure you want to reject all selected redemptions?')
                        ->modalSubmitActionLabel('Yes, reject all')
                        ->form([
                            Forms\Components\Textarea::make('rejection_reason')
                                ->label('Rejection Reason')
                                ->required()
                                ->placeholder('Please provide a reason for rejection...'),
                        ])
                        ->action(function ($records, array $data) {
                            $records->each(function ($record) use ($data) {
                                if ($record->status === 'pending') {
                                    $record->update([
                                        'status' => 'rejected',
                                        'notes' => $data['rejection_reason']
                                    ]);
                                    
                                    // Refund points to the account
                                    $account = $record->account;
                                    $account->point += $record->point_used;
                                    $account->save();
                                    
                                    // Restore gift quantity
                                    $gift = $record->gift;
                                    $gift->quantity += $record->quantity;
                                    $gift->save();
                                }
                            });
                            
                            Notification::make()
                                ->title('Redemptions Rejected')
                                ->body('Points have been refunded and gift quantities restored.')
                                ->success()
                                ->send();
                        }),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListGiftRedemptions::route('/'),
            'create' => Pages\CreateGiftRedemption::route('/create'),
            'view' => Pages\ViewGiftRedemption::route('/{record}'),
            'edit' => Pages\EditGiftRedemption::route('/{record}/edit'),
        ];
    }
}

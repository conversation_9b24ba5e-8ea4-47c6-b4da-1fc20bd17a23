<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title><PERSON><PERSON><PERSON> thực email</title>
    <style>
        body { font-family: 'Segoe UI', <PERSON><PERSON>, sans-serif; background: #f0fdf4; margin: 0; }
        .container { max-width: 420px; margin: 60px auto; background: #fff; border-radius: 16px; box-shadow: 0 4px 24px rgba(16,185,129,0.08); padding: 32px 24px; text-align: center; }
        .brand { font-size: 1.6rem; font-weight: bold; background: linear-gradient(90deg, #10B981, #0EA5E9); color: transparent; background-clip: text; -webkit-background-clip: text; }
        .btn { display: inline-block; background: linear-gradient(90deg, #10B981, #0EA5E9); color: #fff; padding: 12px 32px; border-radius: 8px; font-size: 1.1rem; font-weight: 600; text-decoration: none; margin: 24px 0 16px 0; }
        .footer { margin-top: 32px; color: #6b7280; font-size: 0.95rem; }
    </style>
</head>
<body>
    <div class="container">
        <div class="brand">EcoSolve</div>
        <h2>{{ trans_db('email.vuil_long_xac_thuc_email') }}</h2>
        <p>{{ trans_db('email.lien_ket_da_duoc_gui_toi_email_cua_ban') }}<br>
        {{ trans_db('email.neu_chua_nhan_duoc_yeu_cau_gui_lai') }}</p>
        <form method="POST" action="{{ route('verification.send', ['locale' => app()->getLocale()]) }}">
            @csrf
            <button type="submit" class="btn">{{ trans_db('auth.resend_verification') }}</button>
        </form>
        <div class="footer">
            {{ trans_db('email.tran_trong') }}<br>
            {{ trans_db('common.doi_ngu_ecosolve') }}
        </div>
    </div>
</x-guest-layout>

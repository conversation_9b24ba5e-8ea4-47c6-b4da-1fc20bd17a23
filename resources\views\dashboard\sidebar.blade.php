<div class="w-1/4 border-r bg-gray-50 p-6 flex flex-col gap-4">
    <a href="/">{{ trans_db('nav.trang_chu') }}</a>
    <a href="{{ route('dashboard.profile', ['locale' => app()->getLocale()]) }}" class="text-gray-800 font-semibold hover:text-[#10B981] transition {{ request()->routeIs('dashboard.profile') ? 'text-[#10B981]' : '' }}">{{ trans_db('nav.profile') }}</a>
    <a href="{{ route('dashboard.statistics', ['locale' => app()->getLocale()]) }}" class="text-gray-800 font-semibold hover:text-[#10B981] transition {{ request()->routeIs('dashboard.statistics') ? 'text-[#10B981]' : '' }}">{{ trans_db('general.thong_ke') }}</a>
    <a href="{{ route('dashboard.events', ['locale' => app()->getLocale()]) }}" class="text-gray-800 font-semibold hover:text-[#10B981] transition {{ request()->routeIs('dashboard.events') ? 'text-[#10B981]' : '' }}">{{ trans_db('nav.events') }}</a>
    <a href="{{ route('dashboard.gifts', ['locale' => app()->getLocale()]) }}" class="text-gray-800 font-semibold hover:text-[#10B981] transition {{ request()->routeIs('dashboard.gifts') ? 'text-[#10B981]' : '' }}">{{ trans_db('general.qua_tang') }}</a>
    <form method="POST" action="{{ route('logout', ['locale' => app()->getLocale()]) }}" class="inline">
        @csrf
        <button type="submit" class="text-red-600 font-semibold hover:underline text-left btn-logout">{{ trans_db('general.dang_xuat') }}</button>
    </form>
</div>

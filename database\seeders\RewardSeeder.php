<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Reward;

class RewardSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $rewards = [
            [
                'name' => 'Voucher Shopee 50K',
                'description' => 'Voucher giảm giá Shopee trị giá 50.000 VNĐ, áp dụng cho đơn hàng từ 100.000 VNĐ',
                'image' => null,
                'point_cost' => 100,
                'quantity' => 50,
                'is_active' => true,
            ],
            [
                'name' => 'Voucher Grab 30K',
                'description' => 'Voucher giảm giá Grab trị giá 30.000 VNĐ, áp dụng cho dịch vụ giao hàng và đi xe',
                'image' => null,
                'point_cost' => 80,
                'quantity' => 30,
                'is_active' => true,
            ],
            [
                'name' => 'Thẻ cào điện thoại 20K',
                'description' => 'Thẻ cào điện thoại trị giá 20.000 VNĐ, áp dụng cho tất cả nhà mạng',
                'image' => null,
                'point_cost' => 60,
                'quantity' => 100,
                'is_active' => true,
            ],
            [
                'name' => 'Voucher Tiki 100K',
                'description' => 'Voucher giảm giá Tiki trị giá 100.000 VNĐ, áp dụng cho đơn hàng từ 200.000 VNĐ',
                'image' => null,
                'point_cost' => 200,
                'quantity' => 20,
                'is_active' => true,
            ],
            [
                'name' => 'Ly sứ cao cấp',
                'description' => 'Ly sứ cao cấp với thiết kế đẹp, dung tích 350ml, phù hợp cho cà phê và trà',
                'image' => null,
                'point_cost' => 150,
                'quantity' => 25,
                'is_active' => true,
            ],
            [
                'name' => 'Bình nước giữ nhiệt',
                'description' => 'Bình nước giữ nhiệt 500ml, giữ nóng 12 giờ, giữ lạnh 24 giờ',
                'image' => null,
                'point_cost' => 300,
                'quantity' => 15,
                'is_active' => true,
            ],
            [
                'name' => 'Túi vải canvas',
                'description' => 'Túi vải canvas thân thiện môi trường, kích thước 40x35cm, có in logo ứng dụng',
                'image' => null,
                'point_cost' => 120,
                'quantity' => 40,
                'is_active' => true,
            ],
            [
                'name' => 'Mũ bảo hiểm',
                'description' => 'Mũ bảo hiểm xe máy chất lượng cao, đạt tiêu chuẩn an toàn, có nhiều màu sắc',
                'image' => null,
                'point_cost' => 400,
                'quantity' => 10,
                'is_active' => true,
            ],
            [
                'name' => 'Voucher CGV 2D',
                'description' => 'Voucher xem phim CGV 2D trị giá 80.000 VNĐ, áp dụng cho tất cả rạp CGV',
                'image' => null,
                'point_cost' => 160,
                'quantity' => 35,
                'is_active' => true,
            ],
            [
                'name' => 'Sổ tay bảo vệ môi trường',
                'description' => 'Sổ tay với thiết kế đẹp, giấy tái chế, có in các tips bảo vệ môi trường',
                'image' => null,
                'point_cost' => 90,
                'quantity' => 60,
                'is_active' => true,
            ],
            [
                'name' => 'Voucher Starbucks 50K',
                'description' => 'Voucher Starbucks trị giá 50.000 VNĐ, áp dụng cho đồ uống và bánh ngọt',
                'image' => null,
                'point_cost' => 120,
                'quantity' => 25,
                'is_active' => true,
            ],
            [
                'name' => 'Bộ bút viết cao cấp',
                'description' => 'Bộ bút viết 3 cây với thiết kế đẹp, mực chất lượng cao, phù hợp cho học tập và công việc',
                'image' => null,
                'point_cost' => 180,
                'quantity' => 30,
                'is_active' => true,
            ],
            [
                'name' => 'Voucher Pizza Hut 100K',
                'description' => 'Voucher Pizza Hut trị giá 100.000 VNĐ, áp dụng cho pizza và đồ uống',
                'image' => null,
                'point_cost' => 250,
                'quantity' => 15,
                'is_active' => true,
            ],
            [
                'name' => 'Áo thun cotton',
                'description' => 'Áo thun cotton 100%, có in logo ứng dụng, nhiều size và màu sắc',
                'image' => null,
                'point_cost' => 220,
                'quantity' => 20,
                'is_active' => true,
            ],
            [
                'name' => 'Voucher Vincom 200K',
                'description' => 'Voucher Vincom trị giá 200.000 VNĐ, áp dụng cho mua sắm tại các trung tâm Vincom',
                'image' => null,
                'point_cost' => 500,
                'quantity' => 8,
                'is_active' => true,
            ],
        ];

        foreach ($rewards as $reward) {
            Reward::create($reward);
        }
    }
}

@extends('layouts.app')
@section('title', trans_db('general.dien_dan') ?? '<PERSON><PERSON><PERSON> đàn')
@section('header', trans_db('general.dien_dan') ?? '<PERSON><PERSON>n đàn')
@section('content')
    <div class="container mx-auto py-8 px-4">
        <div class="flex items-center justify-between mb-6">
            <h2 class="text-2xl font-bold bg-gradient-to-r from-[#10B981] to-[#0EA5E9] bg-clip-text text-transparent">
                {{ trans_db('general.danh_sach_chu_de') ?? 'Danh sách chủ đề' }}
            </h2>
            @if (Auth::guard('account')->check())
                <a href="{{ route('forum.create', ['locale' => app()->getLocale()]) }}"
                    class="px-4 py-2 rounded-lg bg-gradient-to-r from-[#10B981] to-[#0EA5E9] text-white font-semibold hover:from-[#0EA5E9] hover:to-[#10B981] transition">
                    {{ trans_db('action.tao_chu_de_moi') ?? 'Tạo chủ đề mới' }}
                </a>
            @endif
        </div>

        <div class="bg-white rounded-2xl shadow-2xl divide-y divide-gray-100">
            @forelse($threads as $thread)
                <a href="{{ route('forum.show', ['locale' => app()->getLocale(), 'slug' => $thread->slug]) }}"
                   class="block p-6 hover:bg-[#f0fdfa] transition group">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-bold text-[#0EA5E9] group-hover:underline">{{ $thread->title }}</h3>
                        <span class="text-xs text-gray-400">{{ $thread->created_at->diffForHumans() }}</span>
                    </div>
                    <div class="text-gray-600 mt-1 mb-2">{{ Str::limit($thread->content, 100) }}</div>
                    <div class="flex items-center gap-4 text-xs text-gray-500">
                        <span>
                            {{ trans_db('general.dang_boi') ?? 'Đăng bởi' }}:
                            <span class="font-semibold text-[#10B981]">
                                {{ $thread->author->full_name ?? ($thread->author->username ?? trans_db('general.an_danh') ?? 'Ẩn danh') }}
                            </span>
                        </span>
                        <span>{{ trans_db('general.phan_hoi') ?? 'Phản hồi' }}: {{ $thread->replies_count ?? 0 }}</span>
                        <span>{{ trans_db('general.luot_xem') ?? 'Lượt xem' }}: {{ $thread->views ?? 0 }}</span>
                    </div>
                </a>
            @empty
                <div class="p-6 text-center text-gray-500">
                    {{ trans_db('common.chua_co_chu_de_nao') ?? 'Chưa có chủ đề nào' }}
                </div>
            @endforelse
        </div>

        <!-- Pagination if needed -->
        @if(method_exists($threads, 'links'))
            <div class="mt-6">
                {{ $threads->links() }}
            </div>
        @endif
    </div>
@endsection

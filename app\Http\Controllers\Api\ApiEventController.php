<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Event;
use App\Models\EventRegistration;

class ApiEventController extends Controller
{
    public function register(Request $request)
    {
        $request->validate([
            'event_id' => 'required|exists:events,id',
        ]);
        $accountId = auth('account')->id();
        if (!$accountId) {
            return response()->json(['success' => false, 'message' => 'Bạn cần đăng nhập!'], 401);
        }
        $eventId = $request->event_id;
        $exists = EventRegistration::where('event_id', $eventId)->where('account_id', $accountId)->exists();
        if ($exists) {
            return response()->json(['success' => false, 'message' => 'Bạn đã đăng ký sự kiện này!']);
        }
        EventRegistration::create([
            'event_id' => $eventId,
            'account_id' => $accountId,
            'registered_at' => now(),
        ]);
        return response()->json(['success' => true, 'message' => 'Đăng ký tham gia sự kiện thành công!']);
    }
} 
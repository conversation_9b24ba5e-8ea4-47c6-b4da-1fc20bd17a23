@props([
    'name' => '',
    'id' => [],
    'selected' => null,
    'placeholder' => false,
    'disabled' => null,
    'helpText' => null
])

@php
    $id = $id ?? $name;
@endphp

<div {{ $attributes->merge(['class-4']) }}>
    @if($label)
        <label for="{{ $id }}" class="block text-sm font-medium text-gray-700 mb-1">
            {{ $label }}
            @if($required)
                <span class="text-red-500">*</span>
            @endif
        </label>
    @endif

    <div class="relative }}" 
            id="{{ $id }}" 
            @if($required) required @endif
            @if($disabled) disabled @endif
            {{ $attributes->merge(['class ' . ($error ? 'border-red-300' : 'border-gray-300') . ' bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm' : '')]) }}
        >
            @if($placeholder)
                <option value="" @if($selected === null) selected @endif>{{ $placeholder }}</option>
            @endif

            @foreach($options as $value => $label)
                <option value="{{ $value }}" @if($selected !== null && $selected == $value) selected @endif>
                    {{ $label }}
                </option>
            @endforeach
        </select>

        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
            <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" clip-rule="evenodd-1 text-sm text-red-600">{{ $error }}</p>
    @endif

    @if($helpText)
        <p class="mt-1 text-sm text-gray-500">{{ $helpText }}</p>
    @endif
</div>
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EventRegistration extends Model
{
    use HasFactory;

    protected $fillable = [
        'event_id',
        'account_id',
        'registered_at',
        'note',
    ];

    protected $dates = [
        'registered_at',
    ];

    // Quan hệ tới Event
    public function event()
    {
        return $this->belongsTo(Event::class);
    }

    // Quan hệ tới Account
    public function account()
    {
        return $this->belongsTo(Account::class);
    }
}

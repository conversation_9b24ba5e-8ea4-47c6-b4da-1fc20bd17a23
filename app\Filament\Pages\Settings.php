<?php

namespace App\Filament\Pages;

use App\Models\User;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Support\Facades\Cache;

class Settings extends Page
{
    use InteractsWithForms;
    
    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';
    
    protected static ?string $navigationLabel = 'Site Settings';
    
    protected static ?int $navigationSort = 100;
    
    protected static string $view = 'filament.pages.settings';
    
    public ?array $data = [];
    
    public function mount(): void
    {
        $this->form->fill([
            'site_name' => Cache::get('site_name', 'EcoSolves'),
            'site_description' => Cache::get('site_description', 'A Laravel Blog Application'),
            'site_logo' => Cache::get('site_logo'),
            'site_favicon' => Cache::get('site_favicon'),
            'footer_text' => Cache::get('footer_text', '© ' . date('Y') . ' EcoSolves. All rights reserved.'),
            'enable_registration' => Cache::get('enable_registration', true),
            'contact_email' => Cache::get('contact_email', '<EMAIL>'),
            'contact_phone' => Cache::get('contact_phone', '+1234567890'),
            'contact_address' => Cache::get('contact_address', '123 Street, City, Country'),
            'facebook_url' => Cache::get('facebook_url'),
            'twitter_url' => Cache::get('twitter_url'),
            'instagram_url' => Cache::get('instagram_url'),
            'linkedin_url' => Cache::get('linkedin_url'),
            'youtube_url' => Cache::get('youtube_url'),
            'meta_title' => Cache::get('meta_title', 'EcoSolves - A Laravel Blog Application'),
            'meta_description' => Cache::get('meta_description', 'EcoSolves is a Laravel blog application with a powerful admin panel.'),
            'meta_keywords' => Cache::get('meta_keywords', 'laravel, blog, EcoSolves'),
        ]);
    }
    
    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('General Settings')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('site_name')
                                    ->label('Site Name')
                                    ->required(),
                                    
                                TextInput::make('site_description')
                                    ->label('Site Description')
                                    ->required(),
                            ]),
                            
                        Grid::make(2)
                            ->schema([
                                FileUpload::make('site_logo')
                                    ->label('Site Logo')
                                    ->image()
                                    ->directory('settings')
                                    ->visibility('public'),
                                    
                                FileUpload::make('site_favicon')
                                    ->label('Site Favicon')
                                    ->image()
                                    ->directory('settings')
                                    ->visibility('public'),
                            ]),
                            
                        Textarea::make('footer_text')
                            ->label('Footer Text')
                            ->rows(2)
                            ->required(),
                            
                        Toggle::make('enable_registration')
                            ->label('Enable User Registration')
                            ->default(true),
                    ]),
                    
                Section::make('Contact Information')
                    ->schema([
                        Grid::make(3)
                            ->schema([
                                TextInput::make('contact_email')
                                    ->label('Contact Email')
                                    ->email(),
                                    
                                TextInput::make('contact_phone')
                                    ->label('Contact Phone'),
                                    
                                TextInput::make('contact_address')
                                    ->label('Contact Address'),
                            ]),
                    ]),
                    
                Section::make('Social Media')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('facebook_url')
                                    ->label('Facebook URL')
                                    ->url(),
                                    
                                TextInput::make('twitter_url')
                                    ->label('Twitter URL')
                                    ->url(),
                                    
                                TextInput::make('instagram_url')
                                    ->label('Instagram URL')
                                    ->url(),
                                    
                                TextInput::make('linkedin_url')
                                    ->label('LinkedIn URL')
                                    ->url(),
                                    
                                TextInput::make('youtube_url')
                                    ->label('YouTube URL')
                                    ->url(),
                            ]),
                    ]),
                    
                Section::make('SEO Settings')
                    ->schema([
                        TextInput::make('meta_title')
                            ->label('Meta Title')
                            ->required(),
                            
                        Textarea::make('meta_description')
                            ->label('Meta Description')
                            ->rows(2)
                            ->required(),
                            
                        TextInput::make('meta_keywords')
                            ->label('Meta Keywords')
                            ->required(),
                    ]),
            ])
            ->statePath('data');
    }
    
    public function save(): void
    {
        $data = $this->form->getState();
        
        foreach ($data as $key => $value) {
            Cache::put($key, $value);
        }
        
        Notification::make()
            ->title('Settings saved successfully')
            ->success()
            ->send();
    }
}

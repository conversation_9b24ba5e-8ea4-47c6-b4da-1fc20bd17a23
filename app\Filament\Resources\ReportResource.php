<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ReportResource\Pages;
use App\Models\Report;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Str;
use Filament\Forms\Components;

class ReportResource extends Resource
{
    protected static ?string $model = Report::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    
    protected static ?string $navigationGroup = 'Content Management';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Components\Section::make('Thông tin báo cáo')
                    ->schema([
                        Components\FileUpload::make('image')
                            ->label('Ảnh điểm rác')
                            ->directory('reports/images')
                            ->image()
                            ->required(),
                        Components\Textarea::make('description')
                            ->label('Mô tả')
                            ->required(),
                        Components\TextInput::make('latitude')
                            ->label('<PERSON><PERSON> độ')
                            ->required()
                            ->numeric(),
                        Components\TextInput::make('longitude')
                            ->label('Kinh độ')
                            ->required()
                            ->numeric(),
                        Components\TextInput::make('gmap_link')
                            ->label('Link Google Maps')
                            ->nullable(),
                        Components\Select::make('status')
                            ->label('Trạng thái')
                            ->options([
                                'pending' => 'Chờ duyệt',
                                'approved' => 'Đã duyệt',
                                'rejected' => 'Từ chối',
                            ])
                            ->required()
                            ->default('pending'),
                        Components\Select::make('created_by')
                            ->label('Người tạo')
                            ->relationship('creator', 'username')
                            ->searchable()
                            ->required(),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('image')
                    ->label('Ảnh')
                    ->width(60)
                    ->height(60),
                Tables\Columns\TextColumn::make('description')
                    ->label('Mô tả')
                    ->limit(40),
                Tables\Columns\TextColumn::make('latitude')
                    ->label('Vĩ độ'),
                Tables\Columns\TextColumn::make('longitude')
                    ->label('Kinh độ'),
                Tables\Columns\TextColumn::make('gmap_link')
                    ->label('Google Maps')
                    ->limit(20),
                Tables\Columns\TextColumn::make('status')
                    ->label('Trạng thái')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'pending' => 'gray',
                        'approved' => 'success',
                        'rejected' => 'danger',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('creator.name')
                    ->label('Người tạo')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Trạng thái')
                    ->options([
                        'pending' => 'Chờ duyệt',
                        'approved' => 'Đã duyệt',
                        'rejected' => 'Từ chối',
                    ]),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListReports::route('/'),
            'create' => Pages\CreateReport::route('/create'),
            'edit' => Pages\EditReport::route('/{record}/edit'),
        ];
    }
}

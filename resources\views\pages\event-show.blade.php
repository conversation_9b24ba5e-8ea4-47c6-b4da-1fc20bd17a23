@extends('layouts.app')
@section('title', $event->title)
@section('header', $event->title)
@section('content')
    <div class="container mx-auto py-8 max-w-3xl">
        <div class="bg-white rounded-2xl shadow-2xl p-6 border border-[#10B981]/20">
            @if ($event->featured_image)
                <img src="{{ asset('storage/' . $event->featured_image) }}" alt="{{ $event->title }}"
                    class="rounded mb-4 w-full h-64 object-cover border border-[#10B981]/30">
            @else
                <img src="https://placehold.co/600x400?text=No+Image" alt="No Image"
                    class="rounded mb-4 w-full h-64 object-cover border border-[#10B981]/30">
            @endif
            <h1 class="text-3xl font-bold bg-gradient-to-r from-[#10B981] to-[#0EA5E9] bg-clip-text text-transparent mb-2">
                {{ $event->title }}</h1>
            <div class="text-sm text-gray-500 mb-2">
                <span class="font-medium text-[#10B981]">{{ trans_db('time.thoi_gian') }}</span>
                {{ \Carbon\Carbon::parse($event->start_date)->format('d/m/Y H:i') }}
                @if ($event->end_date)
                    - {{ \Carbon\Carbon::parse($event->end_date)->format('d/m/Y H:i') }}
                @endif
            </div>
            <div class="text-sm text-gray-500 mb-2">
                <span class="font-medium text-[#0EA5E9]">{{ trans_db('general.dia_diem') }}</span> {{ $event->location }}
            </div>
            @if ($event->max_participants)
                <div class="text-xs text-gray-400 mb-2">
                    <span class="font-medium">{{ trans_db('general.so_nguoi_toi_da') }}:</span> {{ $event->max_participants }}
                </div>
            @endif
            <div class="text-gray-700 mb-4">
                <strong>{{ trans_db('general.mo_ta') }}:</strong> {{ $event->description }}
            </div>
            <div class="prose max-w-none mb-6">
                {!! nl2br(e($event->content)) !!}
            </div>
            <button id="register-btn" data-id="{{ $event->id }}"
                class="w-full py-3 rounded-lg text-white font-semibold transition {{ $registered ? 'bg-gray-400 cursor-not-allowed' : 'bg-gradient-to-r from-[#10B981] to-[#0EA5E9] hover:from-[#0EA5E9] hover:to-[#10B981]' }}"
                @if ($registered) disabled @endif>
                {{ $registered ? trans_db('nav.da_dang_ky_tham_gia') : trans_db('general.tham_gia_su_kien') }}
            </button>
        </div>
    </div>
@endsection

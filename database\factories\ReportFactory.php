<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Report>
 */
class ReportFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // Lấy random account id nếu có
        $accountId = \App\Models\Account::inRandomOrder()->first()?->id ?? 1;
        $lat = $this->faker->latitude(8, 23);
        $lng = $this->faker->longitude(102, 110);
        return [
            'created_by' => $accountId,
            'image' => 'reports/images/' . $this->faker->uuid . '.jpg',
            'description' => $this->faker->sentence(10),
            'latitude' => $lat,
            'longitude' => $lng,
            'gmap_link' => "https://maps.google.com/?q={$lat},{$lng}",
            'status' => $this->faker->randomElement(['pending', 'approved', 'rejected']),
        ];
    }
}

<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Post;
use App\Models\Category;

class PostController extends Controller
{
    /**
     * Display a listing of the posts.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $query = Post::with('category')->latest();
        
        // Filter by category if provided
        if ($request->has('category_id')) {
            $query->where('category_id', $request->category_id);
        }
        
        $posts = $query->paginate(12);
        $categories = Category::orderBy('name')->get();
        $featuredPost = Post::where('is_featured', true)->latest()->first();
        
        return view('posts.index', compact('posts', 'categories', 'featuredPost'));
    }

    /**
     * Display the specified post.
     *
     * @param  string  $slug
     * @return \Illuminate\Http\Response
     */
    public function show($slug)
    {
        $post = Post::with('category')->where('slug', $slug)->firstOrFail();
        
        // Get previous and next posts
        $previousPost = Post::where('id', '<', $post->id)->orderBy('id', 'desc')->first();
        $nextPost = Post::where('id', '>', $post->id)->orderBy('id')->first();
        
        // Get related posts (same category, excluding current post)
        $relatedPosts = Post::where('category_id', $post->category_id)
            ->where('id', '!=', $post->id)
            ->latest()
            ->take(4)
            ->get();
        
        return view('posts.show', compact('post', 'previousPost', 'nextPost', 'relatedPosts'));
    }
}
@extends('layouts.app')
@section('title', $thread->title ?? trans_db('general.dien_dan'))
@section('header', trans_db('general.dien_dan') ?? 'Diễn đàn')
@section('content')
    <div class="container mx-auto py-8 px-4">
        <div class="max-w-3xl mx-auto">
            <!-- Back to forum link -->
            <div class="mb-4">
                <a href="{{ route('forum.index', ['locale' => app()->getLocale()]) }}"
                   class="text-[#10B981] hover:text-[#0EA5E9] transition flex items-center gap-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                    {{ trans_db('general.quay_lai_dien_dan') ?? 'Quay lại diễn đàn' }}
                </a>
            </div>

            <!-- Success/Error Messages -->
            @if (session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    {{ session('success') }}
                </div>
            @endif

            @if (session('error'))
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    {{ session('error') }}
                </div>
            @endif

            <!-- Thread content -->
            <div class="bg-white rounded-2xl shadow-2xl p-6 mb-6">
                <div class="flex justify-between items-start mb-2">
                    <h1 class="text-2xl font-bold bg-gradient-to-r from-[#10B981] to-[#0EA5E9] bg-clip-text text-transparent">
                        {{ $thread->title ?? trans_db('general.khong_co_tieu_de') ?? 'Không có tiêu đề' }}
                    </h1>

                    @if(Auth::guard('account')->check() && Auth::guard('account')->id() == $thread->account_id)
                        <div class="flex gap-2">
                            <button class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                {{ trans_db('action.chinh_sua') ?? 'Chỉnh sửa' }}
                            </button>
                            <button class="text-red-600 hover:text-red-800 text-sm font-medium">
                                {{ trans_db('action.xoa') ?? 'Xóa' }}
                            </button>
                        </div>
                    @endif
                </div>
                <div class="flex items-center gap-4 text-xs text-gray-500 mb-4">
                    <span>
                        {{ trans_db('general.dang_boi') ?? 'Đăng bởi' }}:
                        <span class="font-semibold text-[#10B981]">
                            {{ $thread->account->full_name ?? ($thread->account->username ?? trans_db('general.an_danh') ?? 'Ẩn danh') }}
                        </span>
                    </span>
                    <span>{{ $thread->created_at->diffForHumans() }}</span>
                    <span>{{ trans_db('general.luot_xem') ?? 'Lượt xem' }}: {{ $thread->views ?? 0 }}</span>
                    <span>{{ trans_db('general.phan_hoi') ?? 'Phản hồi' }}: {{ $thread->replies_count ?? 0 }}</span>
                </div>
                <div class="text-gray-700 leading-relaxed prose max-w-none">
                    {!! nl2br(e($thread->content ?? trans_db('general.khong_co_noi_dung') ?? 'Không có nội dung')) !!}
                </div>
            </div>

            <!-- Replies section -->
            <div class="bg-white rounded-2xl shadow-2xl p-6">
                <h2 class="text-lg font-semibold text-[#0EA5E9] mb-4">
                    {{ trans_db('general.phan_hoi') ?? 'Phản hồi' }} ({{ $thread->replies_count ?? 0 }})
                </h2>

                @if(isset($thread->replies) && $thread->replies->count() > 0)
                    <div class="space-y-4">
                        @foreach($thread->replies as $reply)
                            <div class="border-b border-gray-100 pb-4 last:border-b-0">
                                <div class="flex items-center gap-2 text-xs text-gray-500 mb-2">
                                    <span class="font-semibold text-[#10B981]">
                                        {{ $reply->account->full_name ?? ($reply->account->username ?? trans_db('general.an_danh') ?? 'Ẩn danh') }}
                                    </span>
                                    <span>{{ $reply->created_at->diffForHumans() }}</span>
                                </div>
                                <div class="text-gray-700 leading-relaxed">
                                    {!! nl2br(e($reply->content)) !!}
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-gray-500 text-center py-4">
                        {{ trans_db('common.chua_co_phan_hoi_nao') ?? 'Chưa có phản hồi nào' }}
                    </p>
                @endif
            </div>

            @if (Auth::guard('account')->check())
                <!-- Reply form -->
                <div class="bg-white rounded-2xl shadow-2xl p-6 mt-6">
                    <h2 class="text-lg font-semibold text-[#10B981] mb-4">
                        {{ trans_db('general.viet_phan_hoi') ?? 'Viết phản hồi' }}
                    </h2>
                    <form action="{{ route('forum.reply', ['locale' => app()->getLocale(), 'slug' => $thread->slug]) }}" method="POST" id="reply-form">
                        @csrf
                        <input type="hidden" name="thread_id" value="{{ $thread->id }}">
                        <div class="mb-4">
                            <label for="content" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ trans_db('general.noi_dung_phan_hoi') ?? 'Nội dung phản hồi' }} <span class="text-red-500">*</span>
                            </label>
                            <textarea name="content"
                                      id="content"
                                      rows="4"
                                      class="w-full rounded-lg border border-gray-300 focus:border-[#10B981] focus:ring-[#10B981] px-4 py-3"
                                      placeholder="{{ trans_db('general.nhap_noi_dung_phan_hoi') ?? 'Nhập nội dung phản hồi' }}"
                                      required>{{ old('content') }}</textarea>
                            @error('content')
                                <div class="text-red-500 text-sm mt-1">{{ $message }}</div>
                            @enderror
                            <div class="text-xs text-gray-500 mt-1">
                                {{ trans_db('general.toi_thieu_5_ky_tu') ?? 'Tối thiểu 5 ký tự' }}
                            </div>
                        </div>
                        <div class="flex justify-between items-center">
                            <div class="text-sm text-gray-600">
                                {{ trans_db('general.dang_nhap_voi') ?? 'Đăng nhập với' }}:
                                <span class="font-semibold text-[#10B981]">{{ Auth::guard('account')->user()->full_name }}</span>
                            </div>
                            <div class="flex gap-3">
                                <button type="button"
                                        onclick="document.getElementById('content').value=''"
                                        class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg font-semibold hover:bg-gray-300 transition">
                                    {{ trans_db('action.xoa_noi_dung') ?? 'Xóa' }}
                                </button>
                                <button type="submit"
                                        class="px-6 py-2 bg-gradient-to-r from-[#10B981] to-[#0EA5E9] text-white rounded-lg font-semibold hover:from-[#0EA5E9] hover:to-[#10B981] transition">
                                    {{ trans_db('action.gui_phan_hoi') ?? 'Gửi phản hồi' }}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            @else
                <div class="bg-gray-50 rounded-2xl p-6 mt-6 text-center">
                    <p class="text-gray-600 mb-4">
                        {{ trans_db('auth.dang_nhap_de_phan_hoi') ?? 'Đăng nhập để phản hồi' }}
                    </p>
                    <a href="{{ route('login', ['locale' => app()->getLocale()]) }}"
                       class="inline-block px-6 py-2 bg-gradient-to-r from-[#10B981] to-[#0EA5E9] text-white rounded-lg font-semibold hover:from-[#0EA5E9] hover:to-[#10B981] transition">
                        {{ trans_db('auth.login') ?? 'Đăng nhập' }}
                    </a>
                </div>
            @endif
        </div>
    </div>
@endsection

<?php

namespace App\Http\Controllers;

use App\Models\Thread;
use App\Models\Reply;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class ForumController extends Controller
{
    public function __construct() {}

    public function index()
    {
        $threads = Thread::with('account')
            ->withCount('replies')
            ->orderByDesc('created_at')
            ->get();
        return view('forum.index', compact('threads'));
    }

    public function create() {
        if (!Auth::guard('account')->check()) {
            return redirect()->route('login', ['locale' => app()->getLocale()]);
        }
        return view('forum.create');
    }

    public function store(Request $request)
    {
        if (!Auth::guard('account')->check()) {
            return redirect()->route('login', ['locale' => app()->getLocale()]);
        }

        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string|min:10',
        ]);

        try {
            $thread = new Thread();
            $thread->title = $request->title;
            $thread->content = $request->content;
            $thread->slug = Str::slug($request->title) . '-' . time();
            $thread->account_id = Auth::guard('account')->id();
            $thread->save();

            return redirect()->route('forum.show', ['locale' => app()->getLocale(), 'slug' => $thread->slug])
                ->with('success', trans_db('status.tao_chu_de_thanh_cong') ?? 'Tạo chủ đề thành công!');
        } catch (\Exception $e) {
            return back()->with('error', trans_db('status.co_loi_xay_ra') ?? 'Có lỗi xảy ra!')
                ->withInput();
        }
    }

    public function show($locale, $slug) {

        $thread = Thread::with(['account', 'replies.account'])
            ->where('slug', $slug)
            ->firstOrFail();

        // Increment view count
        $thread->increment('views');

        // Load replies với account relationship
        $thread->load(['replies' => function($query) {
            $query->with('account')->orderBy('created_at');
        }]);

        $thread->replies_count = $thread->replies->count();

        return view('forum.show', compact('thread'));
    }

    public function reply(Request $request, $locale, $slug)
    {
        if (!Auth::guard('account')->check()) {
            return redirect()->route('login', ['locale' => app()->getLocale()]);
        }

        $request->validate([
            'content' => 'required|string|min:5',
            'thread_id' => 'required|exists:threads,id',
        ]);

        try {
            $reply = new Reply();
            $reply->content = $request->content;
            $reply->thread_id = $request->thread_id;
            $reply->account_id = Auth::guard('account')->id();
            $reply->save();

            // Update thread replies count
            $threadModel = Thread::find($request->thread_id);
            if ($threadModel) {
                $threadModel->increment('replies_count');
            }

            return back()->with('success', trans_db('status.gui_phan_hoi_thanh_cong') ?? 'Gửi phản hồi thành công!');
        } catch (\Exception $e) {
            return back()->with('error', trans_db('status.co_loi_xay_ra') ?? 'Có lỗi xảy ra!');
        }
    }


}

@props([
    'type' => 'button',
    'color' => 'primary',
    'size' => 'md',
    'href' => null,
    'disabled' => false
])

@php
    $baseClasses = 'inline-flex items-center justify-center font-semibold tracking-widest focus:outline-none focus:ring transition ease-in-out duration-150';

    $colorClasses = match($color) {
        'primary' => 'bg-indigo-600 border border-transparent text-white hover:bg-indigo-700 active:bg-indigo-800 focus:border-indigo-800 focus:ring-indigo-300',
        'secondary' => 'bg-gray-200 border border-transparent text-gray-700 hover:bg-gray-300 active:bg-gray-400 focus:border-gray-400 focus:ring-gray-200',
        'success' => 'bg-green-600 border border-transparent text-white hover:bg-green-700 active:bg-green-800 focus:border-green-800 focus:ring-green-300',
        'danger' => 'bg-red-600 border border-transparent text-white hover:bg-red-700 active:bg-red-800 focus:border-red-800 focus:ring-red-300',
        'warning' => 'bg-yellow-500 border border-transparent text-white hover:bg-yellow-600 active:bg-yellow-700 focus:border-yellow-700 focus:ring-yellow-200',
        'info' => 'bg-blue-500 border border-transparent text-white hover:bg-blue-600 active:bg-blue-700 focus:border-blue-700 focus:ring-blue-200',
        'light' => 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 active:bg-gray-100 focus:border-gray-300 focus:ring-gray-100',
        'dark' => 'bg-gray-800 border border-transparent text-white hover:bg-gray-900 active:bg-gray-900 focus:border-gray-900 focus:ring-gray-300',
        'link' => 'bg-transparent border-0 text-indigo-600 hover:text-indigo-800 hover:underline focus:ring-indigo-100',
        default => 'bg-indigo-600 border border-transparent text-white hover:bg-indigo-700 active:bg-indigo-800 focus:border-indigo-800 focus:ring-indigo-300',
    };

    $sizeClasses = match($size) {
        'xs' => 'px-2.5 py-1.5 text-xs rounded',
        'sm' => 'px-3 py-1.5 text-sm rounded-md',
        'md' => 'px-4 py-2 text-sm rounded-md',
        'lg' => 'px-5 py-2.5 text-base rounded-md',
        'xl' => 'px-6 py-3 text-base rounded-md',
        default => 'px-4 py-2 text-sm rounded-md',
    };

    $disabledClasses = $disabled ? 'opacity-50 cursor-not-allowed' : '';

    $classes = "$baseClasses $colorClasses $sizeClasses $disabledClasses";
@endphp

@if($href && !$disabled)
    <a href="{{ $href }}" {{ $attributes->merge(['class' => $classes]) }}>
        {{ $slot }}
    </a>
@else
    <button type="{{ $type }}" {{ $attributes->merge(['class' => $classes, 'disabled' => $disabled]) }}>
        {{ $slot }}
    </button>
@endif

<?php

namespace App\Filament\Widgets;

use App\Models\Post;
use App\Models\Category;
use App\Models\Tag;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class StatsOverview extends BaseWidget
{
    protected function getStats(): array
    {
        return [
            Stat::make('Total Posts', Post::count())
                ->description('Total number of posts')
                ->descriptionIcon('heroicon-m-document-text')
                ->color('success'),
            Stat::make('Categories', Category::count())
                ->description('Total number of categories')
                ->descriptionIcon('heroicon-m-rectangle-stack')
                ->color('primary'),
            Stat::make('Tags', Tag::count())
                ->description('Total number of tags')
                ->descriptionIcon('heroicon-m-tag')
                ->color('warning'),
        ];
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reports', function (Blueprint $table) {
            $table->id();
            $table->foreignId('created_by')->nullable()->constrained('accounts')->nullOnDelete();
            $table->string('image'); // Đường dẫn ảnh điểm rác
            $table->text('description'); // <PERSON>ô tả
            $table->double('latitude', 10, 7); // <PERSON><PERSON> độ
            $table->double('longitude', 10, 7); // Kinh độ
            $table->string('gmap_link')->nullable(); // Link Google Maps nếu có
            $table->string('status')->default('pending'); // pending, approved, rejected
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reports');
    }
};
